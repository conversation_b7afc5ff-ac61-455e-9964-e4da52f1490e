<?php
/**
 * Sidebar Navigation for Hakim Clinic Management System
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

if (!isLoggedIn()) {
    return;
}

$currentUser = getCurrentUser();
$currentPage = basename($_SERVER['PHP_SELF'], '.php');
?>

<nav class="col-lg-2 d-lg-block sidebar collapse" id="sidebar">
    <div class="position-sticky pt-3">
        <!-- Clinic Info -->
        <?php if ($currentClinic): ?>
            <div class="text-center mb-4 p-3 bg-white bg-opacity-10 rounded">
                <?php if ($currentClinic['logo']): ?>
                    <img src="/assets/uploads/<?php echo $currentClinic['logo']; ?>" 
                         alt="<?php echo htmlspecialchars($currentClinic['name']); ?>" 
                         class="img-fluid rounded-circle mb-2" 
                         style="width: 60px; height: 60px; object-fit: cover;">
                <?php else: ?>
                    <div class="bg-white bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" 
                         style="width: 60px; height: 60px;">
                        <i class="fas fa-clinic-medical fa-2x text-white"></i>
                    </div>
                <?php endif; ?>
                <h6 class="text-white mb-1"><?php echo htmlspecialchars($currentClinic['name']); ?></h6>
                <small class="text-white-50">
                    <?php echo __($currentClinic['subscription_plan']); ?>
                    <?php 
                    $daysRemaining = getSubscriptionDaysRemaining();
                    if ($daysRemaining <= 7 && $daysRemaining > 0): 
                    ?>
                        <br><span class="badge bg-warning"><?php echo $daysRemaining; ?> <?php echo __('days_remaining'); ?></span>
                    <?php elseif ($daysRemaining <= 0): ?>
                        <br><span class="badge bg-danger"><?php echo __('expired'); ?></span>
                    <?php endif; ?>
                </small>
            </div>
        <?php endif; ?>
        
        <!-- Navigation Menu -->
        <ul class="nav flex-column">
            <!-- Dashboard -->
            <li class="nav-item">
                <a class="nav-link <?php echo $currentPage === 'index' || $currentPage === 'dashboard' ? 'active' : ''; ?>" 
                   href="/dashboard/">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    <?php echo __('dashboard'); ?>
                </a>
            </li>
            
            <!-- Patients -->
            <?php if (hasPermission('patients')): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($currentPage, 'patient') !== false ? 'active' : ''; ?>" 
                       href="/pages/patients/">
                        <i class="fas fa-users me-2"></i>
                        <?php echo __('patients'); ?>
                    </a>
                </li>
            <?php endif; ?>
            
            <!-- Appointments -->
            <?php if (hasPermission('appointments')): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($currentPage, 'appointment') !== false ? 'active' : ''; ?>" 
                       href="/pages/appointments/">
                        <i class="fas fa-calendar-alt me-2"></i>
                        <?php echo __('appointments'); ?>
                    </a>
                </li>
            <?php endif; ?>
            
            <!-- Medical Records -->
            <?php if (hasPermission('medical_records')): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($currentPage, 'medical') !== false ? 'active' : ''; ?>" 
                       href="/pages/medical-records/">
                        <i class="fas fa-file-medical me-2"></i>
                        <?php echo __('medical_records'); ?>
                    </a>
                </li>
            <?php endif; ?>
            
            <!-- Prescriptions -->
            <?php if (hasPermission('prescriptions')): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($currentPage, 'prescription') !== false ? 'active' : ''; ?>" 
                       href="/pages/prescriptions/">
                        <i class="fas fa-prescription-bottle-alt me-2"></i>
                        <?php echo __('prescriptions'); ?>
                    </a>
                </li>
            <?php endif; ?>
            
            <!-- Billing -->
            <?php if (hasPermission('billing')): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($currentPage, 'billing') !== false || strpos($currentPage, 'invoice') !== false ? 'active' : ''; ?>" 
                       href="/pages/billing/">
                        <i class="fas fa-file-invoice-dollar me-2"></i>
                        <?php echo __('billing'); ?>
                    </a>
                </li>
            <?php endif; ?>
            
            <!-- Reports -->
            <?php if (hasPermission('reports') || hasRole('admin') || hasRole('doctor')): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($currentPage, 'report') !== false ? 'active' : ''; ?>" 
                       href="/pages/reports/">
                        <i class="fas fa-chart-bar me-2"></i>
                        <?php echo __('reports'); ?>
                    </a>
                </li>
            <?php endif; ?>
            
            <!-- Divider -->
            <hr class="my-3 bg-white bg-opacity-25">
            
            <!-- Admin Section -->
            <?php if (hasRole('admin')): ?>
                <li class="nav-item">
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                        <span><?php echo __('administration'); ?></span>
                    </h6>
                </li>
                
                <!-- Users Management -->
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($currentPage, 'user') !== false ? 'active' : ''; ?>" 
                       href="/pages/users/">
                        <i class="fas fa-user-cog me-2"></i>
                        <?php echo __('users'); ?>
                    </a>
                </li>
                
                <!-- Clinic Settings -->
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($currentPage, 'clinic') !== false ? 'active' : ''; ?>" 
                       href="/pages/clinic/">
                        <i class="fas fa-clinic-medical me-2"></i>
                        <?php echo __('clinic_settings'); ?>
                    </a>
                </li>
                
                <!-- System Settings -->
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($currentPage, 'system') !== false ? 'active' : ''; ?>" 
                       href="/pages/system/">
                        <i class="fas fa-cogs me-2"></i>
                        <?php echo __('system_settings'); ?>
                    </a>
                </li>
                
                <!-- Subscription -->
                <li class="nav-item">
                    <a class="nav-link <?php echo strpos($currentPage, 'subscription') !== false ? 'active' : ''; ?>" 
                       href="/pages/subscription/">
                        <i class="fas fa-credit-card me-2"></i>
                        <?php echo __('subscription'); ?>
                    </a>
                </li>
            <?php endif; ?>
            
            <!-- Divider -->
            <hr class="my-3 bg-white bg-opacity-25">
            
            <!-- Quick Actions -->
            <li class="nav-item">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                    <span><?php echo __('quick_actions'); ?></span>
                </h6>
            </li>
            
            <!-- Add Patient -->
            <?php if (hasPermission('patients')): ?>
                <li class="nav-item">
                    <a class="nav-link" href="/pages/patients/add.php">
                        <i class="fas fa-user-plus me-2"></i>
                        <?php echo __('add_patient'); ?>
                    </a>
                </li>
            <?php endif; ?>
            
            <!-- Book Appointment -->
            <?php if (hasPermission('appointments')): ?>
                <li class="nav-item">
                    <a class="nav-link" href="/pages/appointments/add.php">
                        <i class="fas fa-calendar-plus me-2"></i>
                        <?php echo __('book_appointment'); ?>
                    </a>
                </li>
            <?php endif; ?>
            
            <!-- Create Prescription -->
            <?php if (hasPermission('prescriptions')): ?>
                <li class="nav-item">
                    <a class="nav-link" href="/pages/prescriptions/add.php">
                        <i class="fas fa-prescription me-2"></i>
                        <?php echo __('new_prescription'); ?>
                    </a>
                </li>
            <?php endif; ?>
            
            <!-- Generate Invoice -->
            <?php if (hasPermission('billing')): ?>
                <li class="nav-item">
                    <a class="nav-link" href="/pages/billing/add.php">
                        <i class="fas fa-file-invoice me-2"></i>
                        <?php echo __('generate_invoice'); ?>
                    </a>
                </li>
            <?php endif; ?>
        </ul>
        
        <!-- Footer -->
        <div class="mt-auto p-3 text-center">
            <small class="text-white-50">
                <?php echo __('site_name'); ?><br>
                v<?php echo SITE_VERSION; ?>
            </small>
        </div>
    </div>
</nav>

<!-- Sidebar overlay for mobile -->
<div class="sidebar-overlay d-lg-none" id="sidebarOverlay"></div>

<style>
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    display: none;
}

.sidebar-overlay.show {
    display: block;
}

.sidebar-heading {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(<?php echo getCurrentLanguage() === 'ar' ? '100%' : '-100%'; ?>);
        transition: transform 0.3s ease-in-out;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
}
</style>

<script>
// Mobile sidebar toggle
document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const sidebarOverlay = document.getElementById('sidebarOverlay');
    
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            sidebarOverlay.classList.toggle('show');
        });
    }
    
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', function() {
            sidebar.classList.remove('show');
            sidebarOverlay.classList.remove('show');
        });
    }
});
</script>
