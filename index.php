<?php
/**
 * Main Landing Page for Hakim Clinic Management System
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

require_once 'config/config.php';

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGUAGES)) {
    setLanguage($_GET['lang']);
}

// Redirect to dashboard if already logged in
if (isLoggedIn()) {
    header('Location: /dashboard/');
    exit;
}

$pageTitle = __('welcome');
?>

<!DOCTYPE html>
<html lang="<?php echo getCurrentLanguage(); ?>" dir="<?php echo getCurrentLanguage() === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo __('site_name'); ?> - نظام إدارة العيادات الطبية الأكثر تطوراً">
    <meta name="keywords" content="عيادة, إدارة, طبيب, مرضى, مواعيد, وصفات, فواتير">
    <title><?php echo __('site_name'); ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    
    <!-- Bootstrap CSS -->
    <?php if (getCurrentLanguage() === 'ar'): ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <?php else: ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <?php if (getCurrentLanguage() === 'ar'): ?>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php endif; ?>
    
    <style>
        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #34c759;
            --accent-color: #007bff;
            --gradient-primary: linear-gradient(135deg, #2c5aa0 0%, #007bff 100%);
            --gradient-secondary: linear-gradient(135deg, #34c759 0%, #28a745 100%);
        }
        
        body {
            font-family: <?php echo getCurrentLanguage() === 'ar' ? "'Cairo', sans-serif" : "'Inter', sans-serif"; ?>;
            overflow-x: hidden;
        }
        
        .hero-section {
            background: var(--gradient-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.1)" points="0,1000 1000,0 1000,1000"/></svg>');
            background-size: cover;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .feature-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
        }
        
        .pricing-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .pricing-card.featured {
            transform: scale(1.05);
            border: 3px solid var(--primary-color);
        }
        
        .pricing-card.featured::before {
            content: '<?php echo __("most_popular"); ?>';
            position: absolute;
            top: 1rem;
            right: -2rem;
            background: var(--gradient-secondary);
            color: white;
            padding: 0.5rem 3rem;
            transform: rotate(45deg);
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .btn-primary {
            background: var(--gradient-primary);
            border: none;
            border-radius: 50px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(44, 90, 160, 0.3);
        }
        
        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            border-radius: 50px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-outline-primary:hover {
            background: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .section-padding {
            padding: 5rem 0;
        }
        
        .text-gradient {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stats-counter {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .floating-animation {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .testimonial-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 1rem;
        }
        
        .avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-stethoscope me-2 text-primary"></i>
                <?php echo __('site_name'); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features"><?php echo __('features'); ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#pricing"><?php echo __('pricing'); ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about"><?php echo __('about'); ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact"><?php echo __('contact'); ?></a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <!-- Language Switcher -->
                    <div class="dropdown me-3">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-globe me-1"></i>
                            <?php echo getCurrentLanguage() === 'ar' ? 'العربية' : 'English'; ?>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="?lang=ar">العربية</a></li>
                            <li><a class="dropdown-item" href="?lang=en">English</a></li>
                        </ul>
                    </div>
                    
                    <a href="/pages/auth/login.php" class="btn btn-outline-primary me-2">
                        <?php echo __('login'); ?>
                    </a>
                    <a href="/pages/auth/register.php" class="btn btn-primary">
                        <?php echo __('start_free_trial'); ?>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section text-white">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 hero-content">
                    <h1 class="display-4 fw-bold mb-4">
                        <?php echo __('hero_title'); ?>
                    </h1>
                    <p class="lead mb-4">
                        <?php echo __('hero_subtitle'); ?>
                    </p>
                    <div class="d-flex flex-wrap gap-3">
                        <a href="/pages/auth/register.php" class="btn btn-light btn-lg">
                            <i class="fas fa-rocket me-2"></i>
                            <?php echo __('start_free_trial'); ?>
                        </a>
                        <a href="#features" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-play me-2"></i>
                            <?php echo __('watch_demo'); ?>
                        </a>
                    </div>
                    
                    <!-- Stats -->
                    <div class="row mt-5">
                        <div class="col-4">
                            <div class="stats-counter text-white">500+</div>
                            <small><?php echo __('clinics_trust_us'); ?></small>
                        </div>
                        <div class="col-4">
                            <div class="stats-counter text-white">50K+</div>
                            <small><?php echo __('patients_managed'); ?></small>
                        </div>
                        <div class="col-4">
                            <div class="stats-counter text-white">99.9%</div>
                            <small><?php echo __('uptime'); ?></small>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6 text-center">
                    <div class="floating-animation">
                        <img src="/assets/images/hero-dashboard.png" alt="Dashboard" class="img-fluid" style="max-width: 90%;">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="section-padding bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="display-5 fw-bold text-gradient mb-4"><?php echo __('powerful_features'); ?></h2>
                    <p class="lead text-muted"><?php echo __('features_subtitle'); ?></p>
                </div>
            </div>
            
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h5 class="fw-bold mb-3"><?php echo __('patient_management'); ?></h5>
                        <p class="text-muted"><?php echo __('patient_management_desc'); ?></p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h5 class="fw-bold mb-3"><?php echo __('smart_scheduling'); ?></h5>
                        <p class="text-muted"><?php echo __('smart_scheduling_desc'); ?></p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-prescription-bottle-alt"></i>
                        </div>
                        <h5 class="fw-bold mb-3"><?php echo __('digital_prescriptions'); ?></h5>
                        <p class="text-muted"><?php echo __('digital_prescriptions_desc'); ?></p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-file-invoice-dollar"></i>
                        </div>
                        <h5 class="fw-bold mb-3"><?php echo __('billing_invoicing'); ?></h5>
                        <p class="text-muted"><?php echo __('billing_invoicing_desc'); ?></p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h5 class="fw-bold mb-3"><?php echo __('reports_analytics'); ?></h5>
                        <p class="text-muted"><?php echo __('reports_analytics_desc'); ?></p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h5 class="fw-bold mb-3"><?php echo __('mobile_responsive'); ?></h5>
                        <p class="text-muted"><?php echo __('mobile_responsive_desc'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="display-5 fw-bold text-gradient mb-4"><?php echo __('choose_your_plan'); ?></h2>
                    <p class="lead text-muted"><?php echo __('pricing_subtitle'); ?></p>
                </div>
            </div>
            
            <div class="row g-4 justify-content-center">
                <?php foreach (SUBSCRIPTION_PLANS as $planKey => $plan): ?>
                    <div class="col-lg-3 col-md-6">
                        <div class="pricing-card <?php echo $planKey === 'pro' ? 'featured' : ''; ?>">
                            <div class="text-center">
                                <h5 class="fw-bold mb-3"><?php echo $plan['name']; ?></h5>
                                <div class="mb-4">
                                    <span class="display-4 fw-bold text-primary">
                                        <?php echo $plan['price'] == 0 ? __('free') : formatCurrency($plan['price']); ?>
                                    </span>
                                    <?php if ($plan['price'] > 0): ?>
                                        <span class="text-muted">/ <?php echo __('month'); ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <ul class="list-unstyled mb-4">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        <?php echo $plan['max_patients'] == -1 ? __('unlimited_patients') : $plan['max_patients'] . ' ' . __('patients'); ?>
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        <?php echo $plan['max_doctors'] == -1 ? __('unlimited_doctors') : $plan['max_doctors'] . ' ' . __('doctors'); ?>
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        <?php echo __('basic_features'); ?>
                                    </li>
                                    <?php if (in_array('reports', $plan['features'])): ?>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            <?php echo __('advanced_reports'); ?>
                                        </li>
                                    <?php endif; ?>
                                    <?php if (in_array('sms', $plan['features'])): ?>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            <?php echo __('sms_notifications'); ?>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                                
                                <a href="/pages/auth/register.php?plan=<?php echo $planKey; ?>" 
                                   class="btn <?php echo $planKey === 'pro' ? 'btn-primary' : 'btn-outline-primary'; ?> w-100">
                                    <?php echo $plan['price'] == 0 ? __('start_free_trial') : __('get_started'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-stethoscope me-2"></i>
                        <?php echo __('site_name'); ?>
                    </h5>
                    <p class="text-muted"><?php echo __('footer_description'); ?></p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-white"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3"><?php echo __('product'); ?></h6>
                    <ul class="list-unstyled">
                        <li><a href="#features" class="text-muted text-decoration-none"><?php echo __('features'); ?></a></li>
                        <li><a href="#pricing" class="text-muted text-decoration-none"><?php echo __('pricing'); ?></a></li>
                        <li><a href="#" class="text-muted text-decoration-none"><?php echo __('demo'); ?></a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3"><?php echo __('support'); ?></h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted text-decoration-none"><?php echo __('help_center'); ?></a></li>
                        <li><a href="#contact" class="text-muted text-decoration-none"><?php echo __('contact'); ?></a></li>
                        <li><a href="#" class="text-muted text-decoration-none"><?php echo __('documentation'); ?></a></li>
                    </ul>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3"><?php echo __('contact_info'); ?></h6>
                    <p class="text-muted mb-2">
                        <i class="fas fa-envelope me-2"></i>
                        <EMAIL>
                    </p>
                    <p class="text-muted mb-2">
                        <i class="fas fa-phone me-2"></i>
                        +966 50 123 4567
                    </p>
                    <p class="text-muted">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        المملكة العربية السعودية
                    </p>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; <?php echo date('Y'); ?> <?php echo __('site_name'); ?>. <?php echo __('all_rights_reserved'); ?>.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-muted text-decoration-none me-3"><?php echo __('privacy_policy'); ?></a>
                    <a href="#" class="text-muted text-decoration-none"><?php echo __('terms_of_service'); ?></a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Navbar background on scroll
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            }
        });
        
        // Counter animation
        function animateCounters() {
            const counters = document.querySelectorAll('.stats-counter');
            counters.forEach(counter => {
                const target = parseInt(counter.textContent.replace(/[^0-9]/g, ''));
                const increment = target / 100;
                let current = 0;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        counter.textContent = counter.textContent.replace(/[0-9]+/, target);
                        clearInterval(timer);
                    } else {
                        counter.textContent = counter.textContent.replace(/[0-9]+/, Math.floor(current));
                    }
                }, 20);
            });
        }
        
        // Trigger counter animation when hero section is visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    observer.unobserve(entry.target);
                }
            });
        });
        
        observer.observe(document.querySelector('.hero-section'));
    </script>
</body>
</html>
