{"name": "hakim-clinic-management", "version": "1.0.0", "description": "نظام إدارة العيادات الطبية - Hakim Clinic Management System", "main": "assets/js/app.js", "scripts": {"dev": "npm run watch", "build": "npm run build-css && npm run build-js", "build-css": "sass assets/scss/main.scss assets/css/compiled.css --style compressed", "build-js": "webpack --mode production", "watch": "npm run watch-css & npm run watch-js", "watch-css": "sass --watch assets/scss/main.scss:assets/css/compiled.css", "watch-js": "webpack --mode development --watch", "lint": "eslint assets/js/**/*.js", "lint-fix": "eslint assets/js/**/*.js --fix", "test": "jest", "optimize-images": "imagemin assets/images/src/* --out-dir=assets/images/", "serve": "php -S localhost:8000", "deploy": "npm run build && php scripts/deploy.php"}, "keywords": ["clinic", "medical", "management", "healthcare", "saas", "arabic", "bootstrap", "javascript"], "author": {"name": "Hakim Team", "email": "<EMAIL>", "url": "https://xyz.collectandwin.xyz"}, "license": "MIT", "homepage": "https://xyz.collectandwin.xyz", "repository": {"type": "git", "url": "https://github.com/hakim-team/clinic-management.git"}, "bugs": {"url": "https://github.com/hakim-team/clinic-management/issues", "email": "<EMAIL>"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "babel-loader": "^9.1.0", "css-loader": "^6.8.0", "eslint": "^8.42.0", "eslint-config-standard": "^17.1.0", "imagemin": "^8.0.1", "imagemin-cli": "^7.0.0", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^9.0.2", "imagemin-svgo": "^10.0.1", "jest": "^29.5.0", "mini-css-extract-plugin": "^2.7.6", "sass": "^1.63.0", "style-loader": "^3.3.3", "webpack": "^5.88.0", "webpack-cli": "^5.1.0"}, "dependencies": {"bootstrap": "^5.3.0", "chart.js": "^4.3.0", "datatables.net": "^1.13.4", "datatables.net-bs5": "^1.13.4", "font-awesome": "^4.7.0", "jquery": "^3.7.0", "moment": "^2.29.4", "sweetalert2": "^11.7.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "config": {"port": 8000, "host": "localhost"}, "babel": {"presets": [["@babel/preset-env", {"targets": {"browsers": ["> 1%", "last 2 versions", "not dead"]}}]]}, "eslintConfig": {"extends": ["standard"], "env": {"browser": true, "jquery": true, "es6": true}, "globals": {"bootstrap": "readonly", "Chart": "readonly", "Swal": "readonly", "moment": "readonly", "HakimApp": "writable"}, "rules": {"no-console": "warn", "no-unused-vars": "warn", "semi": ["error", "always"], "quotes": ["error", "single"]}}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/js/setup.js"], "testMatch": ["<rootDir>/tests/js/**/*.test.js"], "collectCoverageFrom": ["assets/js/**/*.js", "!assets/js/vendor/**"]}, "private": true}