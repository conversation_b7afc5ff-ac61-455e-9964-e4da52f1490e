<?php
/**
 * Dashboard Page for Hakim Clinic Management System
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// Require login
requireLogin();

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGUAGES)) {
    setLanguage($_GET['lang']);
}

$currentUser = getCurrentUser();
$currentClinic = getCurrentClinic();
$pageTitle = __('dashboard');

// Get dashboard statistics
$stats = [];

try {
    // Total patients
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM patients WHERE clinic_id = ? AND is_active = 1");
    $stmt->execute([$currentUser['clinic_id']]);
    $stats['total_patients'] = $stmt->fetch()['count'];
    
    // Today's appointments
    $stmt = $db->prepare("
        SELECT COUNT(*) as count 
        FROM appointments 
        WHERE clinic_id = ? AND appointment_date = CURDATE()
    ");
    $stmt->execute([$currentUser['clinic_id']]);
    $stats['today_appointments'] = $stmt->fetch()['count'];
    
    // This month's revenue
    $stmt = $db->prepare("
        SELECT COALESCE(SUM(paid_amount), 0) as revenue 
        FROM invoices 
        WHERE clinic_id = ? AND MONTH(invoice_date) = MONTH(CURDATE()) AND YEAR(invoice_date) = YEAR(CURDATE())
    ");
    $stmt->execute([$currentUser['clinic_id']]);
    $stats['monthly_revenue'] = $stmt->fetch()['revenue'];
    
    // Pending appointments
    $stmt = $db->prepare("
        SELECT COUNT(*) as count 
        FROM appointments 
        WHERE clinic_id = ? AND status IN ('scheduled', 'confirmed') AND appointment_date >= CURDATE()
    ");
    $stmt->execute([$currentUser['clinic_id']]);
    $stats['pending_appointments'] = $stmt->fetch()['count'];
    
    // Recent patients (last 7 days)
    $stmt = $db->prepare("
        SELECT COUNT(*) as count 
        FROM patients 
        WHERE clinic_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    ");
    $stmt->execute([$currentUser['clinic_id']]);
    $stats['new_patients'] = $stmt->fetch()['count'];
    
    // Overdue invoices
    $stmt = $db->prepare("
        SELECT COUNT(*) as count 
        FROM invoices 
        WHERE clinic_id = ? AND status = 'sent' AND due_date < CURDATE()
    ");
    $stmt->execute([$currentUser['clinic_id']]);
    $stats['overdue_invoices'] = $stmt->fetch()['count'];
    
    // Today's appointments details
    $stmt = $db->prepare("
        SELECT a.*, p.first_name, p.last_name, u.full_name as doctor_name
        FROM appointments a
        JOIN patients p ON a.patient_id = p.id
        JOIN users u ON a.doctor_id = u.id
        WHERE a.clinic_id = ? AND a.appointment_date = CURDATE()
        ORDER BY a.appointment_time ASC
        LIMIT 10
    ");
    $stmt->execute([$currentUser['clinic_id']]);
    $todayAppointments = $stmt->fetchAll();
    
    // Recent patients
    $stmt = $db->prepare("
        SELECT * FROM patients 
        WHERE clinic_id = ? 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$currentUser['clinic_id']]);
    $recentPatients = $stmt->fetchAll();
    
    // Upcoming appointments
    $stmt = $db->prepare("
        SELECT a.*, p.first_name, p.last_name, u.full_name as doctor_name
        FROM appointments a
        JOIN patients p ON a.patient_id = p.id
        JOIN users u ON a.doctor_id = u.id
        WHERE a.clinic_id = ? AND a.appointment_date > CURDATE() AND a.status IN ('scheduled', 'confirmed')
        ORDER BY a.appointment_date ASC, a.appointment_time ASC
        LIMIT 5
    ");
    $stmt->execute([$currentUser['clinic_id']]);
    $upcomingAppointments = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $stats = [
        'total_patients' => 0,
        'today_appointments' => 0,
        'monthly_revenue' => 0,
        'pending_appointments' => 0,
        'new_patients' => 0,
        'overdue_invoices' => 0
    ];
    $todayAppointments = [];
    $recentPatients = [];
    $upcomingAppointments = [];
}

include '../includes/header.php';
?>

<div class="main-content">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('dashboard'); ?></h1>
            <p class="text-muted mb-0">
                <?php echo __('welcome_back'); ?>, <?php echo htmlspecialchars($currentUser['full_name']); ?>!
            </p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" onclick="location.reload()">
                <i class="fas fa-sync-alt me-1"></i>
                <?php echo __('refresh'); ?>
            </button>
            <?php if (hasPermission('appointments')): ?>
                <a href="/pages/appointments/add.php" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    <?php echo __('book_appointment'); ?>
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-users text-primary fa-2x"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="h4 mb-0 fw-bold"><?php echo number_format($stats['total_patients']); ?></div>
                            <div class="text-muted"><?php echo __('total_patients'); ?></div>
                            <small class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>
                                +<?php echo $stats['new_patients']; ?> <?php echo __('this_week'); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-calendar-day text-info fa-2x"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="h4 mb-0 fw-bold"><?php echo number_format($stats['today_appointments']); ?></div>
                            <div class="text-muted"><?php echo __('today_appointments'); ?></div>
                            <small class="text-info">
                                <i class="fas fa-clock me-1"></i>
                                <?php echo $stats['pending_appointments']; ?> <?php echo __('pending'); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-dollar-sign text-success fa-2x"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="h4 mb-0 fw-bold"><?php echo formatCurrency($stats['monthly_revenue']); ?></div>
                            <div class="text-muted"><?php echo __('monthly_revenue'); ?></div>
                            <small class="text-success">
                                <i class="fas fa-chart-line me-1"></i>
                                <?php echo __('this_month'); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-exclamation-triangle text-warning fa-2x"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="h4 mb-0 fw-bold"><?php echo number_format($stats['overdue_invoices']); ?></div>
                            <div class="text-muted"><?php echo __('overdue_invoices'); ?></div>
                            <small class="text-warning">
                                <i class="fas fa-clock me-1"></i>
                                <?php echo __('need_attention'); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Row -->
    <div class="row g-4">
        <!-- Today's Appointments -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-day me-2 text-primary"></i>
                        <?php echo __('today_appointments'); ?>
                    </h5>
                    <?php if (hasPermission('appointments')): ?>
                        <a href="/pages/appointments/" class="btn btn-sm btn-outline-primary">
                            <?php echo __('view_all'); ?>
                        </a>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if (empty($todayAppointments)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <p class="text-muted"><?php echo __('no_appointments_today'); ?></p>
                            <?php if (hasPermission('appointments')): ?>
                                <a href="/pages/appointments/add.php" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>
                                    <?php echo __('book_appointment'); ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th><?php echo __('time'); ?></th>
                                        <th><?php echo __('patient'); ?></th>
                                        <th><?php echo __('doctor'); ?></th>
                                        <th><?php echo __('status'); ?></th>
                                        <th><?php echo __('actions'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($todayAppointments as $appointment): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo date('H:i', strtotime($appointment['appointment_time'])); ?></strong>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($appointment['first_name'] . ' ' . $appointment['last_name']); ?>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($appointment['doctor_name']); ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo getAppointmentStatusColor($appointment['status']); ?>">
                                                    <?php echo __($appointment['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if (hasPermission('appointments')): ?>
                                                    <a href="/pages/appointments/view.php?id=<?php echo $appointment['id']; ?>" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Quick Actions & Recent Activity -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2 text-warning"></i>
                        <?php echo __('quick_actions'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if (hasPermission('patients')): ?>
                            <a href="/pages/patients/add.php" class="btn btn-outline-primary">
                                <i class="fas fa-user-plus me-2"></i>
                                <?php echo __('add_patient'); ?>
                            </a>
                        <?php endif; ?>
                        
                        <?php if (hasPermission('appointments')): ?>
                            <a href="/pages/appointments/add.php" class="btn btn-outline-info">
                                <i class="fas fa-calendar-plus me-2"></i>
                                <?php echo __('book_appointment'); ?>
                            </a>
                        <?php endif; ?>
                        
                        <?php if (hasPermission('prescriptions')): ?>
                            <a href="/pages/prescriptions/add.php" class="btn btn-outline-success">
                                <i class="fas fa-prescription me-2"></i>
                                <?php echo __('new_prescription'); ?>
                            </a>
                        <?php endif; ?>
                        
                        <?php if (hasPermission('billing')): ?>
                            <a href="/pages/billing/add.php" class="btn btn-outline-warning">
                                <i class="fas fa-file-invoice me-2"></i>
                                <?php echo __('generate_invoice'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Patients -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-friends me-2 text-success"></i>
                        <?php echo __('recent_patients'); ?>
                    </h5>
                    <?php if (hasPermission('patients')): ?>
                        <a href="/pages/patients/" class="btn btn-sm btn-outline-success">
                            <?php echo __('view_all'); ?>
                        </a>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if (empty($recentPatients)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-user-plus fa-2x text-muted mb-2"></i>
                            <p class="text-muted small"><?php echo __('no_patients_yet'); ?></p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($recentPatients as $patient): ?>
                                <div class="list-group-item border-0 px-0">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-primary bg-opacity-10 rounded-circle p-2">
                                                <i class="fas fa-user text-primary"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <div class="fw-bold">
                                                <?php echo htmlspecialchars($patient['first_name'] . ' ' . $patient['last_name']); ?>
                                            </div>
                                            <small class="text-muted">
                                                <?php echo formatDate($patient['created_at']); ?>
                                            </small>
                                        </div>
                                        <?php if (hasPermission('patients')): ?>
                                            <a href="/pages/patients/view.php?id=<?php echo $patient['id']; ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Upcoming Appointments -->
    <?php if (!empty($upcomingAppointments)): ?>
        <div class="row g-4 mt-2">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent border-0">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-calendar-alt me-2 text-info"></i>
                            <?php echo __('upcoming_appointments'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <?php foreach ($upcomingAppointments as $appointment): ?>
                                <div class="col-md-6 col-lg-4">
                                    <div class="border rounded p-3">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div>
                                                <div class="fw-bold">
                                                    <?php echo htmlspecialchars($appointment['first_name'] . ' ' . $appointment['last_name']); ?>
                                                </div>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars($appointment['doctor_name']); ?>
                                                </small>
                                            </div>
                                            <span class="badge bg-<?php echo getAppointmentStatusColor($appointment['status']); ?>">
                                                <?php echo __($appointment['status']); ?>
                                            </span>
                                        </div>
                                        <div class="text-muted small">
                                            <i class="fas fa-calendar me-1"></i>
                                            <?php echo formatDate($appointment['appointment_date']); ?>
                                            <br>
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo date('H:i', strtotime($appointment['appointment_time'])); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>
