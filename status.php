<?php
/**
 * System Status Check for Hakim Clinic Management System
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالة النظام - حكيم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background: linear-gradient(135deg, #2c5aa0 0%, #007bff 100%);
            min-height: 100vh;
        }
        .status-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-header {
            background: linear-gradient(135deg, #2c5aa0 0%, #007bff 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 20px;
        }
        .check-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        .check-item:last-child {
            border-bottom: none;
        }
        .status-icon {
            width: 30px;
            text-align: center;
            margin-left: 15px;
        }
        .btn-action {
            margin: 10px 5px;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="status-card">
                    <div class="status-header text-center">
                        <h2><i class="fas fa-stethoscope me-3"></i>نظام حكيم - فحص الحالة</h2>
                        <p class="mb-0">تحقق من حالة النظام ومتطلبات التشغيل</p>
                    </div>
                    
                    <div class="card-body p-0">
                        <!-- PHP Version -->
                        <div class="check-item">
                            <div class="d-flex align-items-center flex-grow-1">
                                <div class="status-icon">
                                    <?php if (version_compare(PHP_VERSION, '8.0.0', '>=')): ?>
                                        <i class="fas fa-check-circle text-success fa-lg"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times-circle text-danger fa-lg"></i>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <strong>إصدار PHP</strong>
                                    <div class="text-muted small">الحد الأدنى المطلوب: 8.0</div>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-<?php echo version_compare(PHP_VERSION, '8.0.0', '>=') ? 'success' : 'danger'; ?>">
                                    <?php echo PHP_VERSION; ?>
                                </span>
                            </div>
                        </div>

                        <!-- PDO Extension -->
                        <div class="check-item">
                            <div class="d-flex align-items-center flex-grow-1">
                                <div class="status-icon">
                                    <?php if (extension_loaded('pdo')): ?>
                                        <i class="fas fa-check-circle text-success fa-lg"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times-circle text-danger fa-lg"></i>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <strong>امتداد PDO</strong>
                                    <div class="text-muted small">مطلوب للاتصال بقاعدة البيانات</div>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-<?php echo extension_loaded('pdo') ? 'success' : 'danger'; ?>">
                                    <?php echo extension_loaded('pdo') ? 'متوفر' : 'غير متوفر'; ?>
                                </span>
                            </div>
                        </div>

                        <!-- PDO MySQL -->
                        <div class="check-item">
                            <div class="d-flex align-items-center flex-grow-1">
                                <div class="status-icon">
                                    <?php if (extension_loaded('pdo_mysql')): ?>
                                        <i class="fas fa-check-circle text-success fa-lg"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times-circle text-danger fa-lg"></i>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <strong>امتداد PDO MySQL</strong>
                                    <div class="text-muted small">مطلوب للاتصال بـ MySQL</div>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-<?php echo extension_loaded('pdo_mysql') ? 'success' : 'danger'; ?>">
                                    <?php echo extension_loaded('pdo_mysql') ? 'متوفر' : 'غير متوفر'; ?>
                                </span>
                            </div>
                        </div>

                        <!-- Database Connection -->
                        <?php
                        $dbConnected = false;
                        $dbMessage = '';
                        try {
                            $host = 'localhost';
                            $db_name = 'collectandwin2_vitalsmm';
                            $username = 'collectandwin2_vitalsmm';
                            $password = 'collectandwin2_vitalsmm';
                            
                            $dsn = "mysql:host=$host;dbname=$db_name;charset=utf8mb4";
                            $pdo = new PDO($dsn, $username, $password, [
                                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                            ]);
                            $dbConnected = true;
                            $dbMessage = 'متصل بنجاح';
                        } catch (PDOException $e) {
                            $dbMessage = 'فشل الاتصال: ' . $e->getMessage();
                        }
                        ?>
                        
                        <div class="check-item">
                            <div class="d-flex align-items-center flex-grow-1">
                                <div class="status-icon">
                                    <?php if ($dbConnected): ?>
                                        <i class="fas fa-check-circle text-success fa-lg"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times-circle text-danger fa-lg"></i>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <strong>الاتصال بقاعدة البيانات</strong>
                                    <div class="text-muted small">collectandwin2_vitalsmm</div>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-<?php echo $dbConnected ? 'success' : 'danger'; ?>">
                                    <?php echo $dbConnected ? 'متصل' : 'غير متصل'; ?>
                                </span>
                            </div>
                        </div>

                        <!-- Tables Check -->
                        <?php
                        $tablesExist = false;
                        $tableCount = 0;
                        if ($dbConnected) {
                            try {
                                $stmt = $pdo->query("SHOW TABLES");
                                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                                $tableCount = count($tables);
                                $tablesExist = $tableCount > 0;
                            } catch (Exception $e) {
                                // Tables don't exist
                            }
                        }
                        ?>
                        
                        <div class="check-item">
                            <div class="d-flex align-items-center flex-grow-1">
                                <div class="status-icon">
                                    <?php if ($tablesExist): ?>
                                        <i class="fas fa-check-circle text-success fa-lg"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times-circle text-warning fa-lg"></i>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <strong>جداول قاعدة البيانات</strong>
                                    <div class="text-muted small">الجداول المطلوبة للنظام</div>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-<?php echo $tablesExist ? 'success' : 'warning'; ?>">
                                    <?php echo $tablesExist ? "$tableCount جدول" : 'غير موجودة'; ?>
                                </span>
                            </div>
                        </div>

                        <!-- File Permissions -->
                        <?php
                        $uploadsWritable = is_writable('assets/uploads') || (!file_exists('assets/uploads') && is_writable('assets'));
                        $logsWritable = is_writable('logs') || (!file_exists('logs') && is_writable('.'));
                        ?>
                        
                        <div class="check-item">
                            <div class="d-flex align-items-center flex-grow-1">
                                <div class="status-icon">
                                    <?php if ($uploadsWritable && $logsWritable): ?>
                                        <i class="fas fa-check-circle text-success fa-lg"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times-circle text-warning fa-lg"></i>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <strong>صلاحيات الملفات</strong>
                                    <div class="text-muted small">مجلدات الرفع والسجلات</div>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-<?php echo ($uploadsWritable && $logsWritable) ? 'success' : 'warning'; ?>">
                                    <?php echo ($uploadsWritable && $logsWritable) ? 'صحيحة' : 'تحتاج تعديل'; ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="card-footer bg-light text-center">
                        <?php if (!$dbConnected || !$tablesExist): ?>
                            <a href="setup.php" class="btn btn-primary btn-action">
                                <i class="fas fa-cog me-2"></i>إعداد قاعدة البيانات
                            </a>
                        <?php endif; ?>
                        
                        <a href="test-connection.php" class="btn btn-info btn-action">
                            <i class="fas fa-plug me-2"></i>اختبار الاتصال
                        </a>
                        
                        <?php if ($dbConnected && $tablesExist): ?>
                            <a href="index.php" class="btn btn-success btn-action">
                                <i class="fas fa-home me-2"></i>الانتقال للموقع
                            </a>
                        <?php endif; ?>
                        
                        <button onclick="location.reload()" class="btn btn-secondary btn-action">
                            <i class="fas fa-sync-alt me-2"></i>تحديث
                        </button>
                    </div>
                </div>
                
                <!-- System Info -->
                <div class="status-card mt-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>نظام التشغيل:</strong> <?php echo PHP_OS; ?></p>
                                <p><strong>خادم الويب:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف'; ?></p>
                                <p><strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>الذاكرة المتاحة:</strong> <?php echo ini_get('memory_limit'); ?></p>
                                <p><strong>حد رفع الملفات:</strong> <?php echo ini_get('upload_max_filesize'); ?></p>
                                <p><strong>المنطقة الزمنية:</strong> <?php echo date_default_timezone_get(); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
