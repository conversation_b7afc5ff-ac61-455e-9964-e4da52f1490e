<?php
/**
 * Header Template for Hakim Clinic Management System
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

require_once __DIR__ . '/functions.php';

$currentUser = getCurrentUser();
$currentClinic = getCurrentClinic();
$currentLang = getCurrentLanguage();
$isRTL = $currentLang === 'ar';
?>
<!DOCTYPE html>
<html lang="<?php echo $currentLang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo __('site_name'); ?>">
    <meta name="author" content="Hakim Team">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?><?php echo __('site_name'); ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    
    <!-- Bootstrap CSS -->
    <?php if ($isRTL): ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <?php else: ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <?php if ($isRTL): ?>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Custom CSS -->
    <link href="/assets/css/style.css" rel="stylesheet">
    
    <!-- Additional CSS -->
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link href="<?php echo $css; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <style>
        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #34c759;
            --accent-color: #007bff;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --border-color: #dee2e6;
            --text-muted: #6c757d;
            --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
        }
        
        body {
            font-family: <?php echo $isRTL ? "'Cairo', sans-serif" : "'Inter', sans-serif"; ?>;
            background-color: #f5f6fa;
            color: #2c3e50;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e3c72 100%);
            min-height: 100vh;
            box-shadow: var(--shadow-lg);
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(<?php echo $isRTL ? '-5px' : '5px'; ?>);
        }
        
        .main-content {
            background-color: white;
            border-radius: 1rem;
            box-shadow: var(--shadow);
            margin: 1rem;
            padding: 2rem;
        }
        
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: var(--shadow);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }
        
        .btn {
            border-radius: 0.5rem;
            font-weight: 500;
            padding: 0.5rem 1.5rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(44, 90, 160, 0.3);
        }
        
        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            <?php echo $isRTL ? 'left: -5px;' : 'right: -5px;'; ?>
            background-color: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .language-switcher {
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: background-color 0.3s ease;
        }
        
        .language-switcher:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                <?php echo $isRTL ? 'right: -250px;' : 'left: -250px;'; ?>
                width: 250px;
                height: 100vh;
                z-index: 1050;
                transition: <?php echo $isRTL ? 'right' : 'left'; ?> 0.3s ease;
            }
            
            .sidebar.show {
                <?php echo $isRTL ? 'right: 0;' : 'left: 0;'; ?>
            }
            
            .main-content {
                margin: 0.5rem;
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container-fluid">
            <!-- Mobile menu toggle -->
            <button class="btn btn-outline-primary d-lg-none me-2" type="button" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            
            <!-- Brand -->
            <a class="navbar-brand d-flex align-items-center" href="/dashboard">
                <i class="fas fa-stethoscope me-2"></i>
                <?php echo __('site_name'); ?>
            </a>
            
            <!-- Right side items -->
            <div class="d-flex align-items-center">
                <!-- Language Switcher -->
                <div class="dropdown me-3">
                    <div class="language-switcher" data-bs-toggle="dropdown">
                        <i class="fas fa-globe me-1"></i>
                        <?php echo $currentLang === 'ar' ? 'العربية' : 'English'; ?>
                        <i class="fas fa-chevron-down ms-1"></i>
                    </div>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="?lang=ar">
                                <i class="fas fa-flag me-2"></i>العربية
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="?lang=en">
                                <i class="fas fa-flag me-2"></i>English
                            </a>
                        </li>
                    </ul>
                </div>
                
                <!-- Notifications -->
                <?php if (isLoggedIn()): ?>
                    <div class="dropdown me-3">
                        <button class="btn btn-outline-primary position-relative" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <?php 
                            $unreadCount = getUnreadNotificationsCount($_SESSION['user_id']);
                            if ($unreadCount > 0): 
                            ?>
                                <span class="notification-badge"><?php echo $unreadCount; ?></span>
                            <?php endif; ?>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                            <h6 class="dropdown-header"><?php echo __('notifications'); ?></h6>
                            <div class="dropdown-divider"></div>
                            <?php 
                            $notifications = getRecentNotifications($_SESSION['user_id'], 5);
                            if (empty($notifications)): 
                            ?>
                                <div class="dropdown-item-text text-muted text-center">
                                    <?php echo __('no_data'); ?>
                                </div>
                            <?php else: ?>
                                <?php foreach ($notifications as $notification): ?>
                                    <a class="dropdown-item <?php echo $notification['is_read'] ? '' : 'bg-light'; ?>" 
                                       href="#" onclick="markAsRead(<?php echo $notification['id']; ?>)">
                                        <div class="fw-bold"><?php echo htmlspecialchars($notification['title']); ?></div>
                                        <div class="text-muted small"><?php echo htmlspecialchars($notification['message']); ?></div>
                                        <div class="text-muted small"><?php echo formatDate($notification['created_at']); ?></div>
                                    </a>
                                    <div class="dropdown-divider"></div>
                                <?php endforeach; ?>
                                <a class="dropdown-item text-center" href="/pages/notifications/">
                                    <?php echo __('view_all'); ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- User Menu -->
                <?php if (isLoggedIn()): ?>
                    <div class="dropdown">
                        <button class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($currentUser['full_name']); ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="/pages/profile/">
                                    <i class="fas fa-user me-2"></i><?php echo __('profile'); ?>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/pages/settings/">
                                    <i class="fas fa-cog me-2"></i><?php echo __('settings'); ?>
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="/pages/auth/logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i><?php echo __('logout'); ?>
                                </a>
                            </li>
                        </ul>
                    </div>
                <?php else: ?>
                    <a href="/pages/auth/login.php" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt me-1"></i><?php echo __('login'); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container-fluid">
        <div class="row">
            <?php if (isLoggedIn()): ?>
                <!-- Sidebar -->
                <?php include __DIR__ . '/sidebar.php'; ?>
                
                <!-- Main Content -->
                <main class="col-lg-10 ms-sm-auto px-md-4">
            <?php else: ?>
                <!-- Full width for non-authenticated pages -->
                <main class="col-12">
            <?php endif; ?>
