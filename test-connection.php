<?php
/**
 * Simple Database Connection Test
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database configuration
$host = 'localhost';
$db_name = 'collectandwin2_vitalsmm';
$username = 'collectandwin2_vitalsmm';
$password = 'collectandwin2_vitalsmm';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار الاتصال - حكيم</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>";
echo "<link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap' rel='stylesheet'>";
echo "<style>body { font-family: 'Cairo', sans-serif; }</style>";
echo "</head>";
echo "<body class='bg-light'>";
echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-8'>";
echo "<div class='card'>";
echo "<div class='card-header'>";
echo "<h3>اختبار الاتصال بقاعدة البيانات</h3>";
echo "</div>";
echo "<div class='card-body'>";

// Test 1: PHP Version
echo "<h5>1. إصدار PHP:</h5>";
echo "<p class='text-success'>PHP " . phpversion() . "</p>";

// Test 2: PDO Extension
echo "<h5>2. امتداد PDO:</h5>";
if (extension_loaded('pdo')) {
    echo "<p class='text-success'>✓ متوفر</p>";
} else {
    echo "<p class='text-danger'>✗ غير متوفر</p>";
}

// Test 3: PDO MySQL Extension
echo "<h5>3. امتداد PDO MySQL:</h5>";
if (extension_loaded('pdo_mysql')) {
    echo "<p class='text-success'>✓ متوفر</p>";
} else {
    echo "<p class='text-danger'>✗ غير متوفر</p>";
}

// Test 4: Database Connection
echo "<h5>4. الاتصال بقاعدة البيانات:</h5>";
try {
    $dsn = "mysql:host=$host;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "<p class='text-success'>✓ تم الاتصال بالخادم بنجاح</p>";
    
    // Test 5: Database Exists
    echo "<h5>5. وجود قاعدة البيانات:</h5>";
    $stmt = $pdo->query("SHOW DATABASES LIKE '$db_name'");
    if ($stmt->fetch()) {
        echo "<p class='text-success'>✓ قاعدة البيانات موجودة: $db_name</p>";
        
        // Connect to specific database
        $pdo->exec("USE `$db_name`");
        
        // Test 6: Tables
        echo "<h5>6. الجداول:</h5>";
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($tables)) {
            echo "<p class='text-warning'>⚠ لا توجد جداول في قاعدة البيانات</p>";
            echo "<a href='setup.php' class='btn btn-primary'>إعداد قاعدة البيانات</a>";
        } else {
            echo "<p class='text-success'>✓ الجداول الموجودة:</p>";
            echo "<ul>";
            foreach ($tables as $table) {
                echo "<li>$table</li>";
            }
            echo "</ul>";
            echo "<a href='index.php' class='btn btn-success'>الانتقال للموقع</a>";
        }
        
    } else {
        echo "<p class='text-warning'>⚠ قاعدة البيانات غير موجودة: $db_name</p>";
        echo "<a href='setup.php' class='btn btn-primary'>إنشاء قاعدة البيانات</a>";
    }
    
} catch (PDOException $e) {
    echo "<p class='text-danger'>✗ فشل الاتصال: " . $e->getMessage() . "</p>";
    
    echo "<h6>بيانات الاتصال:</h6>";
    echo "<ul>";
    echo "<li><strong>الخادم:</strong> $host</li>";
    echo "<li><strong>قاعدة البيانات:</strong> $db_name</li>";
    echo "<li><strong>اسم المستخدم:</strong> $username</li>";
    echo "<li><strong>كلمة المرور:</strong> " . str_repeat('*', strlen($password)) . "</li>";
    echo "</ul>";
    
    echo "<h6>الحلول المقترحة:</h6>";
    echo "<ul>";
    echo "<li>تأكد من تشغيل خادم MySQL</li>";
    echo "<li>تحقق من بيانات الاتصال</li>";
    echo "<li>تأكد من وجود صلاحيات الوصول</li>";
    echo "</ul>";
}

// Test 7: File Permissions
echo "<h5>7. صلاحيات الملفات:</h5>";

$directories = ['assets/uploads', 'logs'];
foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        echo "<p class='text-warning'>⚠ المجلد غير موجود: $dir</p>";
        if (mkdir($dir, 0755, true)) {
            echo "<p class='text-success'>✓ تم إنشاء المجلد: $dir</p>";
        } else {
            echo "<p class='text-danger'>✗ فشل إنشاء المجلد: $dir</p>";
        }
    } else {
        if (is_writable($dir)) {
            echo "<p class='text-success'>✓ المجلد قابل للكتابة: $dir</p>";
        } else {
            echo "<p class='text-warning'>⚠ المجلد غير قابل للكتابة: $dir</p>";
        }
    }
}

echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</body>";
echo "</html>";
?>
