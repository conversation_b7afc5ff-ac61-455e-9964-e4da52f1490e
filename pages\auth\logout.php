<?php
/**
 * Logout Page for Hakim Clinic Management System
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

require_once '../../config/config.php';

// Log activity if user is logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    if ($user) {
        logActivity('logout', "User {$user['email']} logged out");
    }
    
    // Clear remember token if exists
    if (isset($_COOKIE['remember_token'])) {
        $stmt = $db->prepare("UPDATE users SET remember_token = NULL WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        
        // Clear cookie
        setcookie('remember_token', '', time() - 3600, '/', '', true, true);
    }
}

// Destroy session
session_destroy();

// Clear all session cookies
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Redirect to login page with success message
header('Location: /pages/auth/login.php?logout=success');
exit;
?>
