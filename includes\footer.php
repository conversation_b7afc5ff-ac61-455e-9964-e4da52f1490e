<?php
/**
 * Footer Template for Hakim Clinic Management System
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
?>
                </main>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-top mt-5 py-4">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; <?php echo date('Y'); ?> <?php echo __('site_name'); ?>. 
                        <?php echo __('all_rights_reserved'); ?>.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">
                        <?php echo __('version'); ?> <?php echo SITE_VERSION; ?> | 
                        <a href="/pages/support/" class="text-decoration-none"><?php echo __('support'); ?></a> |
                        <a href="/pages/privacy/" class="text-decoration-none"><?php echo __('privacy_policy'); ?></a>
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Chart.js for reports -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <link href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Custom JavaScript -->
    <script src="/assets/js/app.js"></script>
    
    <!-- Additional JavaScript -->
    <?php if (isset($additionalJS)): ?>
        <?php foreach ($additionalJS as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <script>
        // Global JavaScript functions and configurations
        
        // Language and RTL support
        const isRTL = <?php echo getCurrentLanguage() === 'ar' ? 'true' : 'false'; ?>;
        const currentLang = '<?php echo getCurrentLanguage(); ?>';
        
        // CSRF Token
        const csrfToken = '<?php echo generateCSRFToken(); ?>';
        
        // API Base URL
        const apiBaseUrl = '/api';
        
        // Common AJAX setup
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': csrfToken
            }
        });
        
        // Language switcher
        $(document).ready(function() {
            $('.language-switcher a').on('click', function(e) {
                e.preventDefault();
                const lang = $(this).attr('href').split('=')[1];
                
                // Store language preference
                localStorage.setItem('preferred_language', lang);
                
                // Redirect with language parameter
                const url = new URL(window.location);
                url.searchParams.set('lang', lang);
                window.location.href = url.toString();
            });
            
            // Load preferred language
            const preferredLang = localStorage.getItem('preferred_language');
            if (preferredLang && preferredLang !== currentLang) {
                const url = new URL(window.location);
                url.searchParams.set('lang', preferredLang);
                window.location.href = url.toString();
            }
        });
        
        // Notification functions
        function markAsRead(notificationId) {
            $.post('/api/notifications/mark-read.php', {
                id: notificationId,
                csrf_token: csrfToken
            }).done(function(response) {
                if (response.success) {
                    // Update notification count
                    updateNotificationCount();
                }
            });
        }
        
        function updateNotificationCount() {
            $.get('/api/notifications/count.php').done(function(response) {
                if (response.success) {
                    const badge = $('.notification-badge');
                    if (response.count > 0) {
                        badge.text(response.count).show();
                    } else {
                        badge.hide();
                    }
                }
            });
        }
        
        // Common form validation
        function validateForm(formId) {
            const form = document.getElementById(formId);
            if (!form) return false;
            
            let isValid = true;
            const requiredFields = form.querySelectorAll('[required]');
            
            requiredFields.forEach(function(field) {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            return isValid;
        }
        
        // Common success/error messages
        function showSuccess(message) {
            Swal.fire({
                icon: 'success',
                title: '<?php echo __("success"); ?>',
                text: message,
                timer: 3000,
                showConfirmButton: false
            });
        }
        
        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: '<?php echo __("error"); ?>',
                text: message
            });
        }
        
        function showWarning(message) {
            Swal.fire({
                icon: 'warning',
                title: '<?php echo __("warning"); ?>',
                text: message
            });
        }
        
        function showInfo(message) {
            Swal.fire({
                icon: 'info',
                title: '<?php echo __("info"); ?>',
                text: message
            });
        }
        
        // Confirmation dialog
        function confirmAction(message, callback) {
            Swal.fire({
                title: '<?php echo __("confirm"); ?>',
                text: message,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: '<?php echo __("yes"); ?>',
                cancelButtonText: '<?php echo __("no"); ?>'
            }).then((result) => {
                if (result.isConfirmed && callback) {
                    callback();
                }
            });
        }
        
        // DataTables default configuration
        $.extend(true, $.fn.dataTable.defaults, {
            language: {
                <?php if (getCurrentLanguage() === 'ar'): ?>
                url: 'https://cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
                <?php else: ?>
                url: 'https://cdn.datatables.net/plug-ins/1.13.4/i18n/en-GB.json'
                <?php endif; ?>
            },
            responsive: true,
            pageLength: 25,
            order: [[0, 'desc']],
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>'
        });
        
        // Auto-hide alerts
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
        
        // Form auto-save (for drafts)
        function enableAutoSave(formId, saveUrl) {
            const form = $('#' + formId);
            let saveTimeout;
            
            form.find('input, textarea, select').on('input change', function() {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(function() {
                    const formData = form.serialize();
                    $.post(saveUrl, formData + '&auto_save=1&csrf_token=' + csrfToken)
                        .done(function(response) {
                            if (response.success) {
                                console.log('Auto-saved');
                            }
                        });
                }, 2000);
            });
        }
        
        // Print function
        function printElement(elementId) {
            const element = document.getElementById(elementId);
            if (!element) return;
            
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title><?php echo __('print'); ?></title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                    <style>
                        body { font-family: <?php echo getCurrentLanguage() === 'ar' ? "'Cairo', sans-serif" : "'Inter', sans-serif"; ?>; }
                        @media print {
                            .no-print { display: none !important; }
                        }
                    </style>
                </head>
                <body>
                    ${element.outerHTML}
                    <script>
                        window.onload = function() {
                            window.print();
                            window.close();
                        };
                    </script>
                </body>
                </html>
            `);
            printWindow.document.close();
        }
        
        // Export to CSV
        function exportToCSV(tableId, filename) {
            const table = document.getElementById(tableId);
            if (!table) return;
            
            let csv = [];
            const rows = table.querySelectorAll('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = [];
                const cols = rows[i].querySelectorAll('td, th');
                
                for (let j = 0; j < cols.length; j++) {
                    row.push('"' + cols[j].innerText.replace(/"/g, '""') + '"');
                }
                
                csv.push(row.join(','));
            }
            
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename + '.csv');
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }
        
        // Check subscription status
        function checkSubscriptionStatus() {
            $.get('/api/subscription/status.php').done(function(response) {
                if (response.expired) {
                    Swal.fire({
                        icon: 'warning',
                        title: '<?php echo __("subscription_expired"); ?>',
                        text: '<?php echo __("subscription_expired_message"); ?>',
                        confirmButtonText: '<?php echo __("renew_subscription"); ?>'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '/pages/subscription/';
                        }
                    });
                } else if (response.expiring_soon) {
                    const toast = Swal.mixin({
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 5000,
                        timerProgressBar: true
                    });
                    
                    toast.fire({
                        icon: 'warning',
                        title: '<?php echo __("subscription_expiring_soon"); ?>'
                    });
                }
            });
        }
        
        // Initialize on page load
        $(document).ready(function() {
            // Check subscription status for logged-in users
            <?php if (isLoggedIn()): ?>
                checkSubscriptionStatus();
            <?php endif; ?>
            
            // Update notification count periodically
            <?php if (isLoggedIn()): ?>
                setInterval(updateNotificationCount, 60000); // Every minute
            <?php endif; ?>
        });
    </script>
</body>
</html>
