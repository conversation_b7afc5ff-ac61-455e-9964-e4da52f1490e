<?php
/**
 * Registration Page for Hakim Clinic Management System
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

require_once '../../config/config.php';

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGUAGES)) {
    setLanguage($_GET['lang']);
}

// Redirect to dashboard if already logged in
if (isLoggedIn()) {
    header('Location: /dashboard/');
    exit;
}

$error = '';
$success = '';
$selectedPlan = $_GET['plan'] ?? 'trial';

// Validate plan
if (!array_key_exists($selectedPlan, SUBSCRIPTION_PLANS)) {
    $selectedPlan = 'trial';
}

// Handle registration form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $clinicName = sanitizeInput($_POST['clinic_name'] ?? '');
    $fullName = sanitizeInput($_POST['full_name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $plan = $_POST['plan'] ?? 'trial';
    $terms = isset($_POST['terms']);
    
    // Validation
    if (empty($clinicName) || empty($fullName) || empty($email) || empty($password)) {
        $error = __('messages.required_fields');
    } elseif (!validateEmail($email)) {
        $error = __('messages.invalid_email');
    } elseif (strlen($password) < PASSWORD_MIN_LENGTH) {
        $error = __('messages.weak_password');
    } elseif ($password !== $confirmPassword) {
        $error = __('messages.password_mismatch');
    } elseif ($phone && !validatePhone($phone)) {
        $error = __('messages.invalid_phone');
    } elseif (!$terms) {
        $error = __('messages.accept_terms');
    } else {
        // Check if email already exists
        $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$email]);
        if ($stmt->fetch()) {
            $error = __('messages.user_exists');
        } else {
            try {
                $db->beginTransaction();
                
                // Create clinic
                $subscriptionStart = date('Y-m-d');
                $subscriptionEnd = date('Y-m-d', strtotime('+' . SUBSCRIPTION_PLANS[$plan]['duration'] . ' days'));
                
                $stmt = $db->prepare("
                    INSERT INTO clinics (name, subscription_plan, subscription_start, subscription_end, is_active) 
                    VALUES (?, ?, ?, ?, 1)
                ");
                $stmt->execute([$clinicName, $plan, $subscriptionStart, $subscriptionEnd]);
                $clinicId = $db->lastInsertId();
                
                // Create admin user
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $db->prepare("
                    INSERT INTO users (username, email, password, full_name, phone, role, clinic_id, is_active) 
                    VALUES (?, ?, ?, ?, ?, 'admin', ?, 1)
                ");
                $stmt->execute([
                    strtolower(str_replace(' ', '', $fullName)),
                    $email,
                    $hashedPassword,
                    $fullName,
                    $phone,
                    $clinicId
                ]);
                $userId = $db->lastInsertId();
                
                // Send welcome notification
                sendNotification(
                    $userId,
                    'welcome',
                    __('welcome_notification_title'),
                    __('welcome_notification_message')
                );
                
                $db->commit();
                
                // Log activity
                logActivity('register', "New clinic registered: $clinicName by $email");
                
                $success = __('messages.registration_success');
                
                // Auto-login
                $_SESSION['user_id'] = $userId;
                $_SESSION['user_email'] = $email;
                $_SESSION['user_name'] = $fullName;
                $_SESSION['user_role'] = 'admin';
                $_SESSION['clinic_id'] = $clinicId;
                
                // Redirect to dashboard after 2 seconds
                header("refresh:2;url=/dashboard/");
                
            } catch (Exception $e) {
                $db->rollBack();
                error_log("Registration error: " . $e->getMessage());
                $error = __('messages.registration_failed');
            }
        }
    }
}

$pageTitle = __('register');
?>

<!DOCTYPE html>
<html lang="<?php echo getCurrentLanguage(); ?>" dir="<?php echo getCurrentLanguage() === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . __('site_name'); ?></title>
    
    <!-- Bootstrap CSS -->
    <?php if (getCurrentLanguage() === 'ar'): ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <?php else: ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <?php if (getCurrentLanguage() === 'ar'): ?>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php endif; ?>
    
    <style>
        :root {
            --primary-color: #2c5aa0;
            --gradient-primary: linear-gradient(135deg, #2c5aa0 0%, #007bff 100%);
        }
        
        body {
            font-family: <?php echo getCurrentLanguage() === 'ar' ? "'Cairo', sans-serif" : "'Inter', sans-serif"; ?>;
            background: var(--gradient-primary);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .register-container {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 1000px;
            width: 100%;
        }
        
        .register-form {
            padding: 3rem;
        }
        
        .register-banner {
            background: var(--gradient-primary);
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .form-control {
            border-radius: 0.5rem;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .btn-primary {
            background: var(--gradient-primary);
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(44, 90, 160, 0.3);
        }
        
        .plan-selector {
            border: 2px solid #e9ecef;
            border-radius: 0.5rem;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .plan-selector:hover {
            border-color: var(--primary-color);
        }
        
        .plan-selector.selected {
            border-color: var(--primary-color);
            background-color: rgba(44, 90, 160, 0.1);
        }
        
        .plan-selector input[type="radio"] {
            display: none;
        }
        
        .language-switcher {
            position: absolute;
            top: 1rem;
            <?php echo getCurrentLanguage() === 'ar' ? 'left: 1rem;' : 'right: 1rem;'; ?>
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            color: #6c757d;
            font-weight: bold;
        }
        
        .step.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .step.completed {
            background-color: #28a745;
            color: white;
        }
        
        .step-line {
            width: 50px;
            height: 2px;
            background-color: #e9ecef;
            margin-top: 19px;
        }
        
        .step-line.completed {
            background-color: #28a745;
        }
        
        @media (max-width: 768px) {
            .register-banner {
                padding: 2rem;
            }
            
            .register-form {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Language Switcher -->
    <div class="language-switcher">
        <div class="dropdown">
            <button class="btn btn-light btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-globe me-1"></i>
                <?php echo getCurrentLanguage() === 'ar' ? 'العربية' : 'English'; ?>
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="?lang=ar&plan=<?php echo $selectedPlan; ?>">العربية</a></li>
                <li><a class="dropdown-item" href="?lang=en&plan=<?php echo $selectedPlan; ?>">English</a></li>
            </ul>
        </div>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-11">
                <div class="register-container">
                    <div class="row g-0">
                        <!-- Registration Banner -->
                        <div class="col-lg-5 d-none d-lg-block">
                            <div class="register-banner h-100">
                                <div class="mb-4">
                                    <i class="fas fa-clinic-medical fa-4x mb-3"></i>
                                    <h3 class="fw-bold mb-3"><?php echo __('join_hakim'); ?></h3>
                                    <p class="mb-4"><?php echo __('register_banner_text'); ?></p>
                                </div>
                                
                                <!-- Selected Plan Info -->
                                <div class="bg-white bg-opacity-20 rounded p-3 w-100">
                                    <h6 class="fw-bold mb-2"><?php echo __('selected_plan'); ?></h6>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><?php echo SUBSCRIPTION_PLANS[$selectedPlan]['name']; ?></span>
                                        <span class="fw-bold">
                                            <?php echo SUBSCRIPTION_PLANS[$selectedPlan]['price'] == 0 ? __('free') : formatCurrency(SUBSCRIPTION_PLANS[$selectedPlan]['price']); ?>
                                        </span>
                                    </div>
                                    <small class="text-white-50">
                                        <?php echo SUBSCRIPTION_PLANS[$selectedPlan]['duration']; ?> <?php echo __('days'); ?>
                                        <?php if (SUBSCRIPTION_PLANS[$selectedPlan]['price'] == 0): ?>
                                            - <?php echo __('free_trial'); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Registration Form -->
                        <div class="col-lg-7">
                            <div class="register-form">
                                <div class="text-center mb-4">
                                    <h2 class="fw-bold text-primary mb-2"><?php echo __('create_account'); ?></h2>
                                    <p class="text-muted"><?php echo __('register_subtitle'); ?></p>
                                </div>
                                
                                <?php if ($error): ?>
                                    <div class="alert alert-danger" role="alert">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <?php echo $error; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($success): ?>
                                    <div class="alert alert-success" role="alert">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <?php echo $success; ?>
                                        <div class="mt-2">
                                            <small><?php echo __('redirecting_to_dashboard'); ?></small>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <form method="POST" id="registerForm">
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                    <input type="hidden" name="plan" value="<?php echo $selectedPlan; ?>">
                                    
                                    <!-- Clinic Information -->
                                    <div class="mb-4">
                                        <h6 class="fw-bold text-primary mb-3">
                                            <i class="fas fa-clinic-medical me-2"></i>
                                            <?php echo __('clinic_information'); ?>
                                        </h6>
                                        
                                        <div class="mb-3">
                                            <label for="clinic_name" class="form-label"><?php echo __('clinic_name'); ?> *</label>
                                            <input type="text" class="form-control" id="clinic_name" name="clinic_name" 
                                                   value="<?php echo htmlspecialchars($_POST['clinic_name'] ?? ''); ?>" 
                                                   placeholder="<?php echo __('enter_clinic_name'); ?>" required>
                                        </div>
                                    </div>
                                    
                                    <!-- Admin Information -->
                                    <div class="mb-4">
                                        <h6 class="fw-bold text-primary mb-3">
                                            <i class="fas fa-user-md me-2"></i>
                                            <?php echo __('admin_information'); ?>
                                        </h6>
                                        
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="full_name" class="form-label"><?php echo __('full_name'); ?> *</label>
                                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                                       value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>" 
                                                       placeholder="<?php echo __('enter_full_name'); ?>" required>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="phone" class="form-label"><?php echo __('phone'); ?></label>
                                                <input type="tel" class="form-control" id="phone" name="phone" 
                                                       value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" 
                                                       placeholder="05xxxxxxxx">
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="email" class="form-label"><?php echo __('email'); ?> *</label>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                                   placeholder="<?php echo __('enter_email'); ?>" required>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="password" class="form-label"><?php echo __('password'); ?> *</label>
                                                <input type="password" class="form-control" id="password" name="password" 
                                                       placeholder="<?php echo __('enter_password'); ?>" required>
                                                <small class="text-muted">
                                                    <?php echo __('password_requirements'); ?>
                                                </small>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="confirm_password" class="form-label"><?php echo __('confirm_password'); ?> *</label>
                                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                                       placeholder="<?php echo __('confirm_password'); ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Terms and Conditions -->
                                    <div class="mb-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                            <label class="form-check-label" for="terms">
                                                <?php echo __('agree_to'); ?>
                                                <a href="/pages/terms/" target="_blank" class="text-decoration-none">
                                                    <?php echo __('terms_of_service'); ?>
                                                </a>
                                                <?php echo __('and'); ?>
                                                <a href="/pages/privacy/" target="_blank" class="text-decoration-none">
                                                    <?php echo __('privacy_policy'); ?>
                                                </a>
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary w-100 mb-3">
                                        <i class="fas fa-rocket me-2"></i>
                                        <?php echo __('create_account'); ?>
                                    </button>
                                </form>
                                
                                <div class="text-center">
                                    <p class="text-muted mb-0">
                                        <?php echo __('already_have_account'); ?>
                                        <a href="login.php" class="text-decoration-none fw-bold">
                                            <?php echo __('login_now'); ?>
                                        </a>
                                    </p>
                                </div>
                                
                                <hr class="my-4">
                                
                                <div class="text-center">
                                    <a href="/" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        <?php echo __('back_to_home'); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form validation
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const clinicName = document.getElementById('clinic_name').value;
            const fullName = document.getElementById('full_name').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const terms = document.getElementById('terms').checked;
            
            // Required fields validation
            if (!clinicName || !fullName || !email || !password) {
                e.preventDefault();
                alert('<?php echo __("messages.required_fields"); ?>');
                return false;
            }
            
            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('<?php echo __("messages.invalid_email"); ?>');
                return false;
            }
            
            // Password validation
            if (password.length < <?php echo PASSWORD_MIN_LENGTH; ?>) {
                e.preventDefault();
                alert('<?php echo __("messages.weak_password"); ?>');
                return false;
            }
            
            // Password confirmation
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('<?php echo __("messages.password_mismatch"); ?>');
                return false;
            }
            
            // Terms acceptance
            if (!terms) {
                e.preventDefault();
                alert('<?php echo __("messages.accept_terms"); ?>');
                return false;
            }
        });
        
        // Password strength indicator
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strength = calculatePasswordStrength(password);
            // You can add visual feedback here
        });
        
        function calculatePasswordStrength(password) {
            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            return strength;
        }
        
        // Auto-hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                if (!alert.classList.contains('alert-success')) {
                    alert.style.transition = 'opacity 0.5s';
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.remove();
                    }, 500);
                }
            });
        }, 5000);
    </script>
</body>
</html>
