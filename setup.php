<?php
/**
 * Database Setup Script for Hakim Clinic Management System
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database configuration
$host = 'localhost';
$db_name = 'collectandwin2_vitalsmm';
$username = 'collectandwin2_vitalsmm';
$password = 'collectandwin2_vitalsmm';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إعداد قاعدة البيانات - حكيم</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>";
echo "<link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap' rel='stylesheet'>";
echo "<style>body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #2c5aa0 0%, #007bff 100%); min-height: 100vh; }</style>";
echo "</head>";
echo "<body class='d-flex align-items-center justify-content-center'>";
echo "<div class='container'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-8'>";
echo "<div class='card shadow-lg'>";
echo "<div class='card-header bg-primary text-white text-center'>";
echo "<h3><i class='fas fa-database me-2'></i>إعداد قاعدة البيانات - نظام حكيم</h3>";
echo "</div>";
echo "<div class='card-body'>";

try {
    // Test database connection
    echo "<div class='alert alert-info'>";
    echo "<h5><i class='fas fa-info-circle me-2'></i>جاري اختبار الاتصال بقاعدة البيانات...</h5>";
    echo "</div>";
    
    $dsn = "mysql:host=$host;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
    
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>تم الاتصال بالخادم بنجاح!";
    echo "</div>";
    
    // Create database if not exists
    echo "<div class='alert alert-info'>";
    echo "<h5><i class='fas fa-database me-2'></i>جاري إنشاء قاعدة البيانات...</h5>";
    echo "</div>";
    
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE `$db_name`");
    
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>تم إنشاء قاعدة البيانات: $db_name";
    echo "</div>";
    
    // Create tables
    echo "<div class='alert alert-info'>";
    echo "<h5><i class='fas fa-table me-2'></i>جاري إنشاء الجداول...</h5>";
    echo "</div>";
    
    // Users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            role ENUM('admin', 'doctor', 'secretary') NOT NULL DEFAULT 'doctor',
            clinic_id INT,
            specialization VARCHAR(100),
            license_number VARCHAR(50),
            avatar VARCHAR(255),
            remember_token VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_clinic_id (clinic_id),
            INDEX idx_role (role),
            INDEX idx_email (email)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Clinics table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS clinics (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            name_en VARCHAR(100),
            address TEXT,
            phone VARCHAR(20),
            email VARCHAR(100),
            website VARCHAR(100),
            logo VARCHAR(255),
            subscription_plan ENUM('trial', 'basic', 'pro', 'enterprise') DEFAULT 'trial',
            subscription_start DATE,
            subscription_end DATE,
            is_active BOOLEAN DEFAULT TRUE,
            settings JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Patients table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS patients (
            id INT AUTO_INCREMENT PRIMARY KEY,
            clinic_id INT NOT NULL,
            patient_number VARCHAR(20) UNIQUE NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            date_of_birth DATE,
            gender ENUM('male', 'female') NOT NULL,
            phone VARCHAR(20),
            email VARCHAR(100),
            address TEXT,
            emergency_contact VARCHAR(100),
            emergency_phone VARCHAR(20),
            blood_type VARCHAR(5),
            allergies TEXT,
            medical_history TEXT,
            insurance_info JSON,
            avatar VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
            INDEX idx_clinic_patient (clinic_id, patient_number),
            INDEX idx_name (first_name, last_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Appointments table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS appointments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            clinic_id INT NOT NULL,
            patient_id INT NOT NULL,
            doctor_id INT NOT NULL,
            appointment_date DATE NOT NULL,
            appointment_time TIME NOT NULL,
            duration INT DEFAULT 30,
            status ENUM('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show') DEFAULT 'scheduled',
            type VARCHAR(50),
            notes TEXT,
            reminder_sent BOOLEAN DEFAULT FALSE,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
            FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
            FOREIGN KEY (doctor_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_clinic_date (clinic_id, appointment_date),
            INDEX idx_doctor_date (doctor_id, appointment_date),
            INDEX idx_patient (patient_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // System settings table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS system_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            description TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Login attempts table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(100) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_email_time (email, created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Notifications table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            type VARCHAR(50) NOT NULL,
            title VARCHAR(200) NOT NULL,
            message TEXT NOT NULL,
            data JSON,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_read (user_id, is_read),
            INDEX idx_created (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>تم إنشاء جميع الجداول بنجاح!";
    echo "</div>";
    
    // Insert initial settings
    echo "<div class='alert alert-info'>";
    echo "<h5><i class='fas fa-cog me-2'></i>جاري إدراج الإعدادات الأولية...</h5>";
    echo "</div>";
    
    $settings = [
        ['site_name', 'حكيم - نظام إدارة العيادات', 'اسم الموقع'],
        ['version', '1.0.0', 'إصدار النظام'],
        ['default_language', 'ar', 'اللغة الافتراضية'],
        ['timezone', 'Asia/Riyadh', 'المنطقة الزمنية']
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO system_settings (setting_key, setting_value, description) 
        VALUES (?, ?, ?) 
        ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
    ");
    
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
    
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i>تم إدراج الإعدادات الأولية بنجاح!";
    echo "</div>";
    
    // Create uploads directory
    $uploadsDir = 'assets/uploads';
    if (!file_exists($uploadsDir)) {
        mkdir($uploadsDir, 0755, true);
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-folder me-2'></i>تم إنشاء مجلد الرفع: $uploadsDir";
        echo "</div>";
    }
    
    // Create logs directory
    $logsDir = 'logs';
    if (!file_exists($logsDir)) {
        mkdir($logsDir, 0755, true);
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-folder me-2'></i>تم إنشاء مجلد السجلات: $logsDir";
        echo "</div>";
    }
    
    echo "<div class='alert alert-success'>";
    echo "<h4><i class='fas fa-check-circle me-2'></i>تم إعداد النظام بنجاح!</h4>";
    echo "<p>يمكنك الآن الانتقال إلى الموقع وإنشاء حساب جديد.</p>";
    echo "</div>";
    
    echo "<div class='text-center mt-4'>";
    echo "<a href='index.php' class='btn btn-primary btn-lg me-3'>";
    echo "<i class='fas fa-home me-2'></i>الانتقال للموقع";
    echo "</a>";
    echo "<a href='pages/auth/register.php' class='btn btn-success btn-lg'>";
    echo "<i class='fas fa-user-plus me-2'></i>إنشاء حساب جديد";
    echo "</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>خطأ في قاعدة البيانات!</h5>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الحلول المقترحة:</strong></p>";
    echo "<ul>";
    echo "<li>تأكد من أن خادم MySQL يعمل</li>";
    echo "<li>تحقق من بيانات الاتصال (اسم المستخدم وكلمة المرور)</li>";
    echo "<li>تأكد من وجود صلاحيات إنشاء قواعد البيانات</li>";
    echo "<li>تحقق من إعدادات الاستضافة</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='alert alert-info'>";
    echo "<h6>بيانات الاتصال المستخدمة:</h6>";
    echo "<ul>";
    echo "<li><strong>الخادم:</strong> $host</li>";
    echo "<li><strong>قاعدة البيانات:</strong> $db_name</li>";
    echo "<li><strong>اسم المستخدم:</strong> $username</li>";
    echo "<li><strong>كلمة المرور:</strong> " . str_repeat('*', strlen($password)) . "</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>خطأ عام!</h5>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "<script src='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js'></script>";
echo "</body>";
echo "</html>";
?>
