<?php
/**
 * Common Functions for Hakim Clinic Management System
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

require_once __DIR__ . '/../config/config.php';

/**
 * Load language strings
 */
function loadLanguage($lang = null) {
    if (!$lang) {
        $lang = getCurrentLanguage();
    }
    
    $langFile = __DIR__ . "/../lang/{$lang}.php";
    if (file_exists($langFile)) {
        return include $langFile;
    }
    
    // Fallback to Arabic
    return include __DIR__ . "/../lang/ar.php";
}

/**
 * Get translated string
 */
function __($key, $default = null) {
    static $translations = null;
    
    if ($translations === null) {
        $translations = loadLanguage();
    }
    
    // Handle nested keys like 'messages.success'
    $keys = explode('.', $key);
    $value = $translations;
    
    foreach ($keys as $k) {
        if (isset($value[$k])) {
            $value = $value[$k];
        } else {
            return $default ?: $key;
        }
    }
    
    return $value;
}

/**
 * Get user information
 */
function getCurrentUser() {
    global $db;
    
    if (!isLoggedIn()) {
        return null;
    }
    
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    return $stmt->fetch();
}

/**
 * Get clinic information
 */
function getCurrentClinic() {
    global $db;
    
    $user = getCurrentUser();
    if (!$user || !$user['clinic_id']) {
        return null;
    }
    
    $stmt = $db->prepare("SELECT * FROM clinics WHERE id = ?");
    $stmt->execute([$user['clinic_id']]);
    return $stmt->fetch();
}

/**
 * Check subscription status
 */
function checkSubscription() {
    $clinic = getCurrentClinic();
    if (!$clinic) {
        return false;
    }
    
    $today = date('Y-m-d');
    return $clinic['subscription_end'] >= $today && $clinic['is_active'];
}

/**
 * Get subscription days remaining
 */
function getSubscriptionDaysRemaining() {
    $clinic = getCurrentClinic();
    if (!$clinic) {
        return 0;
    }
    
    $today = new DateTime();
    $endDate = new DateTime($clinic['subscription_end']);
    $diff = $today->diff($endDate);
    
    return $diff->invert ? 0 : $diff->days;
}

/**
 * Upload file
 */
function uploadFile($file, $allowedTypes = null, $maxSize = null) {
    if (!$allowedTypes) {
        $allowedTypes = ALLOWED_EXTENSIONS;
    }
    
    if (!$maxSize) {
        $maxSize = MAX_FILE_SIZE;
    }
    
    // Check if file was uploaded
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return ['success' => false, 'message' => __('messages.no_file_uploaded')];
    }
    
    // Check file size
    if ($file['size'] > $maxSize) {
        return ['success' => false, 'message' => __('messages.file_too_large')];
    }
    
    // Check file extension
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, $allowedTypes)) {
        return ['success' => false, 'message' => __('messages.invalid_file_type')];
    }
    
    // Generate unique filename
    $filename = generateUniqueId() . '.' . $extension;
    $uploadPath = UPLOAD_PATH . $filename;
    
    // Create upload directory if it doesn't exist
    if (!file_exists(UPLOAD_PATH)) {
        mkdir(UPLOAD_PATH, 0755, true);
    }
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
        return [
            'success' => true,
            'filename' => $filename,
            'path' => $uploadPath,
            'url' => '/assets/uploads/' . $filename
        ];
    }
    
    return ['success' => false, 'message' => __('messages.upload_failed')];
}

/**
 * Delete file
 */
function deleteFile($filename) {
    $filePath = UPLOAD_PATH . $filename;
    if (file_exists($filePath)) {
        return unlink($filePath);
    }
    return false;
}

/**
 * Generate patient number
 */
function generatePatientNumber($clinicId) {
    global $db;
    
    $prefix = 'P' . str_pad($clinicId, 3, '0', STR_PAD_LEFT);
    $year = date('Y');
    
    // Get last patient number for this clinic and year
    $stmt = $db->prepare("
        SELECT patient_number 
        FROM patients 
        WHERE clinic_id = ? AND patient_number LIKE ? 
        ORDER BY id DESC 
        LIMIT 1
    ");
    $stmt->execute([$clinicId, $prefix . $year . '%']);
    $lastPatient = $stmt->fetch();
    
    if ($lastPatient) {
        $lastNumber = intval(substr($lastPatient['patient_number'], -4));
        $newNumber = $lastNumber + 1;
    } else {
        $newNumber = 1;
    }
    
    return $prefix . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
}

/**
 * Generate prescription number
 */
function generatePrescriptionNumber($clinicId) {
    global $db;
    
    $prefix = 'RX' . str_pad($clinicId, 3, '0', STR_PAD_LEFT);
    $year = date('Y');
    
    // Get last prescription number for this clinic and year
    $stmt = $db->prepare("
        SELECT prescription_number 
        FROM prescriptions 
        WHERE prescription_number LIKE ? 
        ORDER BY id DESC 
        LIMIT 1
    ");
    $stmt->execute([$prefix . $year . '%']);
    $lastPrescription = $stmt->fetch();
    
    if ($lastPrescription) {
        $lastNumber = intval(substr($lastPrescription['prescription_number'], -4));
        $newNumber = $lastNumber + 1;
    } else {
        $newNumber = 1;
    }
    
    return $prefix . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
}

/**
 * Generate invoice number
 */
function generateInvoiceNumber($clinicId) {
    global $db;
    
    $prefix = 'INV' . str_pad($clinicId, 3, '0', STR_PAD_LEFT);
    $year = date('Y');
    
    // Get last invoice number for this clinic and year
    $stmt = $db->prepare("
        SELECT invoice_number 
        FROM invoices 
        WHERE clinic_id = ? AND invoice_number LIKE ? 
        ORDER BY id DESC 
        LIMIT 1
    ");
    $stmt->execute([$clinicId, $prefix . $year . '%']);
    $lastInvoice = $stmt->fetch();
    
    if ($lastInvoice) {
        $lastNumber = intval(substr($lastInvoice['invoice_number'], -4));
        $newNumber = $lastNumber + 1;
    } else {
        $newNumber = 1;
    }
    
    return $prefix . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
}

/**
 * Send notification
 */
function sendNotification($userId, $type, $title, $message, $data = null) {
    global $db;
    
    $stmt = $db->prepare("
        INSERT INTO notifications (user_id, type, title, message, data) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    return $stmt->execute([
        $userId,
        $type,
        $title,
        $message,
        $data ? json_encode($data) : null
    ]);
}

/**
 * Get unread notifications count
 */
function getUnreadNotificationsCount($userId) {
    global $db;
    
    $stmt = $db->prepare("
        SELECT COUNT(*) as count 
        FROM notifications 
        WHERE user_id = ? AND is_read = FALSE
    ");
    $stmt->execute([$userId]);
    $result = $stmt->fetch();
    
    return $result['count'];
}

/**
 * Get recent notifications
 */
function getRecentNotifications($userId, $limit = 10) {
    global $db;
    
    $stmt = $db->prepare("
        SELECT * 
        FROM notifications 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT ?
    ");
    $stmt->execute([$userId, $limit]);
    
    return $stmt->fetchAll();
}

/**
 * Mark notification as read
 */
function markNotificationAsRead($notificationId, $userId) {
    global $db;
    
    $stmt = $db->prepare("
        UPDATE notifications 
        SET is_read = TRUE 
        WHERE id = ? AND user_id = ?
    ");
    
    return $stmt->execute([$notificationId, $userId]);
}

/**
 * Calculate age from date of birth
 */
function calculateAge($dateOfBirth) {
    $today = new DateTime();
    $dob = new DateTime($dateOfBirth);
    $age = $today->diff($dob);
    
    return $age->y;
}

/**
 * Get appointment status color
 */
function getAppointmentStatusColor($status) {
    $colors = [
        'scheduled' => 'primary',
        'confirmed' => 'info',
        'in_progress' => 'warning',
        'completed' => 'success',
        'cancelled' => 'danger',
        'no_show' => 'secondary'
    ];
    
    return $colors[$status] ?? 'secondary';
}

/**
 * Get invoice status color
 */
function getInvoiceStatusColor($status) {
    $colors = [
        'draft' => 'secondary',
        'sent' => 'info',
        'paid' => 'success',
        'overdue' => 'danger',
        'cancelled' => 'dark'
    ];
    
    return $colors[$status] ?? 'secondary';
}

/**
 * Format phone number
 */
function formatPhoneNumber($phone) {
    // Remove all non-numeric characters
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // Format Saudi phone numbers
    if (strlen($phone) == 10 && substr($phone, 0, 2) == '05') {
        return '+966 ' . substr($phone, 1, 2) . ' ' . substr($phone, 3, 3) . ' ' . substr($phone, 6, 4);
    }
    
    // Format international numbers starting with +966
    if (strlen($phone) == 12 && substr($phone, 0, 3) == '966') {
        return '+966 ' . substr($phone, 3, 2) . ' ' . substr($phone, 5, 3) . ' ' . substr($phone, 8, 4);
    }
    
    return $phone;
}

/**
 * Validate email
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate phone number
 */
function validatePhone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // Saudi phone number validation
    if (strlen($phone) == 10 && substr($phone, 0, 2) == '05') {
        return true;
    }
    
    // International format
    if (strlen($phone) == 12 && substr($phone, 0, 3) == '966') {
        return true;
    }
    
    return false;
}

/**
 * Get system setting
 */
function getSetting($key, $default = null) {
    global $db;
    
    $stmt = $db->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
    $stmt->execute([$key]);
    $result = $stmt->fetch();
    
    return $result ? $result['setting_value'] : $default;
}

/**
 * Set system setting
 */
function setSetting($key, $value, $description = null) {
    global $db;
    
    $stmt = $db->prepare("
        INSERT INTO system_settings (setting_key, setting_value, description) 
        VALUES (?, ?, ?) 
        ON DUPLICATE KEY UPDATE 
        setting_value = VALUES(setting_value),
        description = COALESCE(VALUES(description), description)
    ");
    
    return $stmt->execute([$key, $value, $description]);
}
?>
