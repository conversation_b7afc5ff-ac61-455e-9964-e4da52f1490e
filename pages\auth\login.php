<?php
/**
 * Login Page for Hakim Clinic Management System
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

require_once '../../config/config.php';

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGUAGES)) {
    setLanguage($_GET['lang']);
}

// Redirect to dashboard if already logged in
if (isLoggedIn()) {
    header('Location: /dashboard/');
    exit;
}

$error = '';
$success = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($email) || empty($password)) {
        $error = __('messages.required_fields');
    } elseif (!validateEmail($email)) {
        $error = __('messages.invalid_email');
    } else {
        // Check login attempts
        $stmt = $db->prepare("
            SELECT COUNT(*) as attempts 
            FROM login_attempts 
            WHERE email = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ");
        $stmt->execute([$email]);
        $attempts = $stmt->fetch()['attempts'];
        
        if ($attempts >= MAX_LOGIN_ATTEMPTS) {
            $error = __('messages.too_many_attempts');
        } else {
            // Verify credentials
            $stmt = $db->prepare("
                SELECT u.*, c.name as clinic_name, c.subscription_end, c.is_active as clinic_active
                FROM users u 
                LEFT JOIN clinics c ON u.clinic_id = c.id 
                WHERE u.email = ? AND u.is_active = 1
            ");
            $stmt->execute([$email]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password'])) {
                // Check clinic subscription
                if ($user['clinic_id'] && (!$user['clinic_active'] || $user['subscription_end'] < date('Y-m-d'))) {
                    $error = __('messages.subscription_expired');
                } else {
                    // Successful login
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_name'] = $user['full_name'];
                    $_SESSION['user_role'] = $user['role'];
                    $_SESSION['clinic_id'] = $user['clinic_id'];
                    
                    // Set remember me cookie
                    if ($remember) {
                        $token = bin2hex(random_bytes(32));
                        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', true, true);
                        
                        // Store token in database
                        $stmt = $db->prepare("UPDATE users SET remember_token = ? WHERE id = ?");
                        $stmt->execute([$token, $user['id']]);
                    }
                    
                    // Clear login attempts
                    $stmt = $db->prepare("DELETE FROM login_attempts WHERE email = ?");
                    $stmt->execute([$email]);
                    
                    // Log activity
                    logActivity('login', "User {$user['email']} logged in");
                    
                    // Redirect to dashboard
                    $redirect = $_GET['redirect'] ?? '/dashboard/';
                    header('Location: ' . $redirect);
                    exit;
                }
            } else {
                // Record failed attempt
                $stmt = $db->prepare("
                    INSERT INTO login_attempts (email, ip_address, created_at) 
                    VALUES (?, ?, NOW())
                ");
                $stmt->execute([$email, $_SERVER['REMOTE_ADDR']]);
                
                $error = __('messages.invalid_credentials');
            }
        }
    }
}

$pageTitle = __('login');
?>

<!DOCTYPE html>
<html lang="<?php echo getCurrentLanguage(); ?>" dir="<?php echo getCurrentLanguage() === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . __('site_name'); ?></title>
    
    <!-- Bootstrap CSS -->
    <?php if (getCurrentLanguage() === 'ar'): ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <?php else: ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <?php if (getCurrentLanguage() === 'ar'): ?>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php endif; ?>
    
    <style>
        :root {
            --primary-color: #2c5aa0;
            --gradient-primary: linear-gradient(135deg, #2c5aa0 0%, #007bff 100%);
        }
        
        body {
            font-family: <?php echo getCurrentLanguage() === 'ar' ? "'Cairo', sans-serif" : "'Inter', sans-serif"; ?>;
            background: var(--gradient-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        
        .login-container {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-form {
            padding: 3rem;
        }
        
        .login-banner {
            background: var(--gradient-primary);
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .form-control {
            border-radius: 0.5rem;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .btn-primary {
            background: var(--gradient-primary);
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(44, 90, 160, 0.3);
        }
        
        .input-group-text {
            background: transparent;
            border: 2px solid #e9ecef;
            border-<?php echo getCurrentLanguage() === 'ar' ? 'left' : 'right'; ?>: none;
        }
        
        .input-group .form-control {
            border-<?php echo getCurrentLanguage() === 'ar' ? 'right' : 'left'; ?>: none;
        }
        
        .input-group:focus-within .input-group-text,
        .input-group:focus-within .form-control {
            border-color: var(--primary-color);
        }
        
        .alert {
            border-radius: 0.5rem;
            border: none;
        }
        
        .language-switcher {
            position: absolute;
            top: 1rem;
            <?php echo getCurrentLanguage() === 'ar' ? 'left: 1rem;' : 'right: 1rem;'; ?>
        }
        
        .floating-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .login-banner {
                padding: 2rem;
            }
            
            .login-form {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Language Switcher -->
    <div class="language-switcher">
        <div class="dropdown">
            <button class="btn btn-light btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-globe me-1"></i>
                <?php echo getCurrentLanguage() === 'ar' ? 'العربية' : 'English'; ?>
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="?lang=ar">العربية</a></li>
                <li><a class="dropdown-item" href="?lang=en">English</a></li>
            </ul>
        </div>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="login-container">
                    <div class="row g-0">
                        <!-- Login Banner -->
                        <div class="col-lg-6 d-none d-lg-block">
                            <div class="login-banner h-100">
                                <div class="floating-icon">
                                    <i class="fas fa-stethoscope"></i>
                                </div>
                                <h3 class="fw-bold mb-3"><?php echo __('welcome_back'); ?></h3>
                                <p class="mb-4"><?php echo __('login_banner_text'); ?></p>
                                <div class="d-flex justify-content-center gap-4 mt-4">
                                    <div class="text-center">
                                        <div class="h4 fw-bold">500+</div>
                                        <small><?php echo __('clinics'); ?></small>
                                    </div>
                                    <div class="text-center">
                                        <div class="h4 fw-bold">50K+</div>
                                        <small><?php echo __('patients'); ?></small>
                                    </div>
                                    <div class="text-center">
                                        <div class="h4 fw-bold">99.9%</div>
                                        <small><?php echo __('uptime'); ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Login Form -->
                        <div class="col-lg-6">
                            <div class="login-form">
                                <div class="text-center mb-4">
                                    <h2 class="fw-bold text-primary mb-2"><?php echo __('login'); ?></h2>
                                    <p class="text-muted"><?php echo __('login_subtitle'); ?></p>
                                </div>
                                
                                <?php if ($error): ?>
                                    <div class="alert alert-danger" role="alert">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <?php echo $error; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($success): ?>
                                    <div class="alert alert-success" role="alert">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <?php echo $success; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <form method="POST" id="loginForm">
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                    
                                    <div class="mb-3">
                                        <label for="email" class="form-label"><?php echo __('email'); ?></label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-envelope"></i>
                                            </span>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                                   placeholder="<?php echo __('enter_email'); ?>" required>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="password" class="form-label"><?php echo __('password'); ?></label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-lock"></i>
                                            </span>
                                            <input type="password" class="form-control" id="password" name="password" 
                                                   placeholder="<?php echo __('enter_password'); ?>" required>
                                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                                <label class="form-check-label" for="remember">
                                                    <?php echo __('remember_me'); ?>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col text-end">
                                            <a href="forgot-password.php" class="text-decoration-none">
                                                <?php echo __('forgot_password'); ?>
                                            </a>
                                        </div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary w-100 mb-3">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        <?php echo __('login'); ?>
                                    </button>
                                </form>
                                
                                <div class="text-center">
                                    <p class="text-muted mb-0">
                                        <?php echo __('dont_have_account'); ?>
                                        <a href="register.php" class="text-decoration-none fw-bold">
                                            <?php echo __('register_now'); ?>
                                        </a>
                                    </p>
                                </div>
                                
                                <hr class="my-4">
                                
                                <div class="text-center">
                                    <a href="/" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        <?php echo __('back_to_home'); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
        
        // Form validation
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                e.preventDefault();
                alert('<?php echo __("messages.required_fields"); ?>');
                return false;
            }
            
            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('<?php echo __("messages.invalid_email"); ?>');
                return false;
            }
        });
        
        // Auto-hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>
