<?php
/**
 * Main Configuration File for Hakim Clinic Management System
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Asia/Riyadh');

// Site configuration
define('SITE_NAME', 'حكيم - نظام إدارة العيادات');
define('SITE_NAME_EN', 'Hakim - Clinic Management System');
define('SITE_URL', 'https://xyz.collectandwin.xyz');
define('SITE_VERSION', '1.0.0');

// Database configuration
require_once __DIR__ . '/database.php';

// File upload settings
define('UPLOAD_PATH', __DIR__ . '/../assets/uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']);

// Language settings
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'en']);

// Security settings
define('PASSWORD_MIN_LENGTH', 8);
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);

// Email settings (configure with your SMTP)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'نظام حكيم');

// Subscription plans
define('SUBSCRIPTION_PLANS', [
    'trial' => [
        'name' => 'تجربة مجانية',
        'name_en' => 'Free Trial',
        'price' => 0,
        'duration' => 7, // days
        'max_patients' => 50,
        'max_doctors' => 2,
        'features' => ['basic_features']
    ],
    'basic' => [
        'name' => 'الخطة الأساسية',
        'name_en' => 'Basic Plan',
        'price' => 99,
        'duration' => 30, // days
        'max_patients' => 500,
        'max_doctors' => 5,
        'features' => ['basic_features', 'reports', 'backup']
    ],
    'pro' => [
        'name' => 'الخطة المتقدمة',
        'name_en' => 'Pro Plan',
        'price' => 199,
        'duration' => 30, // days
        'max_patients' => 2000,
        'max_doctors' => 15,
        'features' => ['basic_features', 'reports', 'backup', 'sms', 'api']
    ],
    'enterprise' => [
        'name' => 'خطة المؤسسات',
        'name_en' => 'Enterprise Plan',
        'price' => 399,
        'duration' => 30, // days
        'max_patients' => -1, // unlimited
        'max_doctors' => -1, // unlimited
        'features' => ['all_features']
    ]
]);

/**
 * Get current language
 */
function getCurrentLanguage() {
    return $_SESSION['language'] ?? DEFAULT_LANGUAGE;
}

/**
 * Set language
 */
function setLanguage($lang) {
    if (in_array($lang, SUPPORTED_LANGUAGES)) {
        $_SESSION['language'] = $lang;
    }
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Check if user has specific role
 */
function hasRole($role) {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === $role;
}

/**
 * Check if user has permission
 */
function hasPermission($permission) {
    $userRole = $_SESSION['user_role'] ?? '';
    
    $permissions = [
        'admin' => ['all'],
        'doctor' => ['patients', 'appointments', 'prescriptions', 'medical_records'],
        'secretary' => ['patients', 'appointments', 'billing']
    ];
    
    return in_array('all', $permissions[$userRole] ?? []) || 
           in_array($permission, $permissions[$userRole] ?? []);
}

/**
 * Redirect to login if not authenticated
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: /pages/auth/login.php');
        exit;
    }
}

/**
 * Redirect to access denied if no permission
 */
function requirePermission($permission) {
    requireLogin();
    if (!hasPermission($permission)) {
        header('Location: /pages/errors/403.php');
        exit;
    }
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Sanitize input
 */
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Format date for display
 */
function formatDate($date, $format = 'Y-m-d') {
    if (getCurrentLanguage() === 'ar') {
        // Arabic date formatting
        $arabicMonths = [
            1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
            5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
            9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
        ];
        
        $timestamp = strtotime($date);
        $day = date('d', $timestamp);
        $month = $arabicMonths[(int)date('m', $timestamp)];
        $year = date('Y', $timestamp);
        
        return "$day $month $year";
    }
    
    return date($format, strtotime($date));
}

/**
 * Format currency
 */
function formatCurrency($amount, $currency = 'SAR') {
    if (getCurrentLanguage() === 'ar') {
        return number_format($amount, 2) . ' ريال';
    }
    return $currency . ' ' . number_format($amount, 2);
}

/**
 * Generate unique ID
 */
function generateUniqueId($prefix = '') {
    return $prefix . date('Ymd') . substr(uniqid(), -6);
}

/**
 * Log activity
 */
function logActivity($action, $details = '') {
    $logFile = __DIR__ . '/../logs/activity.log';
    $timestamp = date('Y-m-d H:i:s');
    $userId = $_SESSION['user_id'] ?? 'guest';
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    $logEntry = "[$timestamp] User: $userId | IP: $ip | Action: $action | Details: $details" . PHP_EOL;
    
    if (!file_exists(dirname($logFile))) {
        mkdir(dirname($logFile), 0755, true);
    }
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Create tables if they don't exist
if ($db) {
    $database->createTables();
}
?>
