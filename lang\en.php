<?php
/**
 * English Language File for Hakim Clinic Management System
 * 
 * <AUTHOR> Team
 * @version 1.0
 */

return [
    // General
    'site_name' => 'Hakim - Clinic Management System',
    'welcome' => 'Welcome',
    'dashboard' => 'Dashboard',
    'home' => 'Home',
    'logout' => 'Logout',
    'login' => 'Login',
    'register' => 'Register',
    'profile' => 'Profile',
    'settings' => 'Settings',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'edit' => 'Edit',
    'add' => 'Add',
    'view' => 'View',
    'search' => 'Search',
    'filter' => 'Filter',
    'export' => 'Export',
    'print' => 'Print',
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    'loading' => 'Loading...',
    'no_data' => 'No data available',
    'success' => 'Success',
    'error' => 'Error',
    'warning' => 'Warning',
    'info' => 'Information',
    'confirm' => 'Confirm',
    'yes' => 'Yes',
    'no' => 'No',
    'required' => 'Required',
    'optional' => 'Optional',
    'select' => 'Select',
    'all' => 'All',
    'none' => 'None',
    'today' => 'Today',
    'yesterday' => 'Yesterday',
    'tomorrow' => 'Tomorrow',
    'this_week' => 'This Week',
    'this_month' => 'This Month',
    'this_year' => 'This Year',
    
    // Authentication
    'username' => 'Username',
    'email' => 'Email',
    'password' => 'Password',
    'confirm_password' => 'Confirm Password',
    'remember_me' => 'Remember Me',
    'forgot_password' => 'Forgot Password?',
    'reset_password' => 'Reset Password',
    'login_success' => 'Login successful',
    'login_failed' => 'Login failed',
    'logout_success' => 'Logout successful',
    'access_denied' => 'Access denied',
    
    // Users & Roles
    'users' => 'Users',
    'user' => 'User',
    'full_name' => 'Full Name',
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'phone' => 'Phone',
    'role' => 'Role',
    'admin' => 'Admin',
    'doctor' => 'Doctor',
    'secretary' => 'Secretary',
    'specialization' => 'Specialization',
    'license_number' => 'License Number',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',
    
    // Clinics
    'clinics' => 'Clinics',
    'clinic' => 'Clinic',
    'clinic_name' => 'Clinic Name',
    'address' => 'Address',
    'website' => 'Website',
    'logo' => 'Logo',
    'subscription' => 'Subscription',
    'subscription_plan' => 'Subscription Plan',
    'subscription_start' => 'Subscription Start',
    'subscription_end' => 'Subscription End',
    'trial' => 'Free Trial',
    'basic' => 'Basic',
    'pro' => 'Pro',
    'enterprise' => 'Enterprise',
    
    // Patients
    'patients' => 'Patients',
    'patient' => 'Patient',
    'patient_number' => 'Patient Number',
    'date_of_birth' => 'Date of Birth',
    'age' => 'Age',
    'gender' => 'Gender',
    'male' => 'Male',
    'female' => 'Female',
    'emergency_contact' => 'Emergency Contact',
    'emergency_phone' => 'Emergency Phone',
    'blood_type' => 'Blood Type',
    'allergies' => 'Allergies',
    'medical_history' => 'Medical History',
    'insurance_info' => 'Insurance Information',
    'add_patient' => 'Add Patient',
    'edit_patient' => 'Edit Patient',
    'patient_details' => 'Patient Details',
    'patient_records' => 'Patient Records',
    
    // Appointments
    'appointments' => 'Appointments',
    'appointment' => 'Appointment',
    'appointment_date' => 'Appointment Date',
    'appointment_time' => 'Appointment Time',
    'duration' => 'Duration',
    'status' => 'Status',
    'scheduled' => 'Scheduled',
    'confirmed' => 'Confirmed',
    'in_progress' => 'In Progress',
    'completed' => 'Completed',
    'cancelled' => 'Cancelled',
    'no_show' => 'No Show',
    'type' => 'Type',
    'notes' => 'Notes',
    'reminder_sent' => 'Reminder Sent',
    'book_appointment' => 'Book Appointment',
    'reschedule' => 'Reschedule',
    'cancel_appointment' => 'Cancel Appointment',
    'appointment_calendar' => 'Appointment Calendar',
    
    // Medical Records
    'medical_records' => 'Medical Records',
    'medical_record' => 'Medical Record',
    'visit_date' => 'Visit Date',
    'chief_complaint' => 'Chief Complaint',
    'diagnosis' => 'Diagnosis',
    'treatment_plan' => 'Treatment Plan',
    'vital_signs' => 'Vital Signs',
    'examination_notes' => 'Examination Notes',
    'follow_up_date' => 'Follow-up Date',
    'attachments' => 'Attachments',
    'blood_pressure' => 'Blood Pressure',
    'temperature' => 'Temperature',
    'pulse' => 'Pulse',
    'weight' => 'Weight',
    'height' => 'Height',
    
    // Prescriptions
    'prescriptions' => 'Prescriptions',
    'prescription' => 'Prescription',
    'prescription_number' => 'Prescription Number',
    'prescription_date' => 'Prescription Date',
    'medications' => 'Medications',
    'medication' => 'Medication',
    'dosage' => 'Dosage',
    'frequency' => 'Frequency',
    'instructions' => 'Instructions',
    'is_printed' => 'Printed',
    'print_prescription' => 'Print Prescription',
    'add_medication' => 'Add Medication',
    'medication_name' => 'Medication Name',
    'strength' => 'Strength',
    'form' => 'Form',
    'manufacturer' => 'Manufacturer',
    
    // Billing & Invoices
    'billing' => 'Billing',
    'invoices' => 'Invoices',
    'invoice' => 'Invoice',
    'invoice_number' => 'Invoice Number',
    'invoice_date' => 'Invoice Date',
    'due_date' => 'Due Date',
    'items' => 'Items',
    'item' => 'Item',
    'quantity' => 'Quantity',
    'price' => 'Price',
    'subtotal' => 'Subtotal',
    'tax' => 'Tax',
    'discount' => 'Discount',
    'total' => 'Total',
    'paid_amount' => 'Paid Amount',
    'remaining_amount' => 'Remaining Amount',
    'payment_method' => 'Payment Method',
    'payment_date' => 'Payment Date',
    'cash' => 'Cash',
    'card' => 'Card',
    'bank_transfer' => 'Bank Transfer',
    'draft' => 'Draft',
    'sent' => 'Sent',
    'paid' => 'Paid',
    'overdue' => 'Overdue',
    'generate_invoice' => 'Generate Invoice',
    'send_invoice' => 'Send Invoice',
    'mark_as_paid' => 'Mark as Paid',
    
    // Reports
    'reports' => 'Reports',
    'report' => 'Report',
    'daily_report' => 'Daily Report',
    'weekly_report' => 'Weekly Report',
    'monthly_report' => 'Monthly Report',
    'yearly_report' => 'Yearly Report',
    'patients_report' => 'Patients Report',
    'appointments_report' => 'Appointments Report',
    'financial_report' => 'Financial Report',
    'statistics' => 'Statistics',
    'total_patients' => 'Total Patients',
    'total_appointments' => 'Total Appointments',
    'total_revenue' => 'Total Revenue',
    'new_patients' => 'New Patients',
    'returning_patients' => 'Returning Patients',
    
    // Notifications
    'notifications' => 'Notifications',
    'notification' => 'Notification',
    'mark_as_read' => 'Mark as Read',
    'mark_all_read' => 'Mark All as Read',
    'appointment_reminder' => 'Appointment Reminder',
    'new_appointment' => 'New Appointment',
    'appointment_cancelled' => 'Appointment Cancelled',
    'payment_received' => 'Payment Received',
    'subscription_expiring' => 'Subscription Expiring Soon',
    
    // Messages
    'messages' => [
        'patient_added' => 'Patient added successfully',
        'patient_updated' => 'Patient updated successfully',
        'patient_deleted' => 'Patient deleted successfully',
        'appointment_booked' => 'Appointment booked successfully',
        'appointment_updated' => 'Appointment updated successfully',
        'appointment_cancelled' => 'Appointment cancelled successfully',
        'prescription_created' => 'Prescription created successfully',
        'invoice_generated' => 'Invoice generated successfully',
        'payment_recorded' => 'Payment recorded successfully',
        'settings_saved' => 'Settings saved successfully',
        'file_uploaded' => 'File uploaded successfully',
        'invalid_file_type' => 'Invalid file type',
        'file_too_large' => 'File too large',
        'required_fields' => 'Please fill all required fields',
        'invalid_email' => 'Invalid email address',
        'password_mismatch' => 'Passwords do not match',
        'weak_password' => 'Password is too weak',
        'user_exists' => 'User already exists',
        'invalid_credentials' => 'Invalid credentials',
        'session_expired' => 'Session expired',
        'unauthorized' => 'Unauthorized access',
        'database_error' => 'Database error',
        'network_error' => 'Network error',
        'unknown_error' => 'Unknown error'
    ]
];
?>
